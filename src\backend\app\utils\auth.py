"""
Authentication Utilities
"""
import jwt
import secrets
import hashlib
from datetime import datetime, timedelta
from functools import wraps
from flask import current_app, request, jsonify, g
from flask_login import current_user

from app.models.user import User
from app.models.api_key import ApiKey


class JWTManager:
    """JWT token management"""
    
    @staticmethod
    def generate_tokens(user):
        """Generate access and refresh tokens for user"""
        now = datetime.utcnow()
        
        # Access token payload
        access_payload = {
            'user_id': str(user.id),
            'email': user.email,
            'role': user.role.value,
            'iat': now,
            'exp': now + current_app.config['JWT_ACCESS_TOKEN_EXPIRES'],
            'type': 'access'
        }
        
        # Refresh token payload
        refresh_payload = {
            'user_id': str(user.id),
            'iat': now,
            'exp': now + current_app.config['JWT_REFRESH_TOKEN_EXPIRES'],
            'type': 'refresh',
            'jti': secrets.token_urlsafe(32)  # JWT ID for revocation
        }
        
        # Generate tokens
        access_token = jwt.encode(
            access_payload,
            current_app.config['JWT_SECRET_KEY'],
            algorithm=current_app.config['JWT_ALGORITHM']
        )
        
        refresh_token = jwt.encode(
            refresh_payload,
            current_app.config['JWT_SECRET_KEY'],
            algorithm=current_app.config['JWT_ALGORITHM']
        )
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'expires_in': int(current_app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
            'token_type': 'Bearer'
        }
    
    @staticmethod
    def decode_token(token, token_type='access'):
        """Decode and validate JWT token"""
        try:
            payload = jwt.decode(
                token,
                current_app.config['JWT_SECRET_KEY'],
                algorithms=[current_app.config['JWT_ALGORITHM']]
            )
            
            # Verify token type
            if payload.get('type') != token_type:
                return None
            
            return payload
        
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    @staticmethod
    def refresh_access_token(refresh_token):
        """Generate new access token from refresh token"""
        payload = JWTManager.decode_token(refresh_token, 'refresh')
        if not payload:
            return None
        
        # Get user
        user = User.get_by_id(payload['user_id'])
        if not user or not user.is_active:
            return None
        
        # Generate new access token
        now = datetime.utcnow()
        access_payload = {
            'user_id': str(user.id),
            'email': user.email,
            'role': user.role.value,
            'iat': now,
            'exp': now + current_app.config['JWT_ACCESS_TOKEN_EXPIRES'],
            'type': 'access'
        }
        
        access_token = jwt.encode(
            access_payload,
            current_app.config['JWT_SECRET_KEY'],
            algorithm=current_app.config['JWT_ALGORITHM']
        )
        
        return {
            'access_token': access_token,
            'expires_in': int(current_app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
            'token_type': 'Bearer'
        }


def jwt_required(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return jsonify({'error': 'Invalid authorization header format'}), 401
        
        if not token:
            return jsonify({'error': 'Access token required'}), 401
        
        payload = JWTManager.decode_token(token)
        if not payload:
            return jsonify({'error': 'Invalid or expired token'}), 401
        
        # Get user
        user = User.get_by_id(payload['user_id'])
        if not user or not user.is_active:
            return jsonify({'error': 'User not found or inactive'}), 401
        
        # Store user in request context
        g.current_user = user
        
        return f(*args, **kwargs)
    
    return decorated_function


def api_key_required(f):
    """Decorator to require API key authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            return jsonify({'error': 'API key required'}), 401
        
        # Validate API key
        key_obj = ApiKey.get_by_key(api_key)
        if not key_obj or not key_obj.is_valid():
            return jsonify({'error': 'Invalid or expired API key'}), 401
        
        # Record usage
        key_obj.record_usage()
        key_obj.save()
        
        # Store user and API key in request context
        g.current_user = key_obj.user
        g.current_api_key = key_obj
        
        return f(*args, **kwargs)
    
    return decorated_function


def auth_required(allow_api_key=True):
    """Decorator to require either JWT or API key authentication"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Try JWT first
            auth_header = request.headers.get('Authorization')
            if auth_header:
                try:
                    token = auth_header.split(' ')[1]
                    payload = JWTManager.decode_token(token)
                    if payload:
                        user = User.get_by_id(payload['user_id'])
                        if user and user.is_active:
                            g.current_user = user
                            return f(*args, **kwargs)
                except (IndexError, AttributeError):
                    pass
            
            # Try API key if allowed
            if allow_api_key:
                api_key = request.headers.get('X-API-Key')
                if api_key:
                    key_obj = ApiKey.get_by_key(api_key)
                    if key_obj and key_obj.is_valid():
                        key_obj.record_usage()
                        key_obj.save()
                        g.current_user = key_obj.user
                        g.current_api_key = key_obj
                        return f(*args, **kwargs)
            
            return jsonify({'error': 'Authentication required'}), 401
        
        return decorated_function
    return decorator


def role_required(*roles):
    """Decorator to require specific user roles"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({'error': 'Authentication required'}), 401
            
            user_role = g.current_user.role.value
            if user_role not in roles and 'owner' not in roles:
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def permission_required(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({'error': 'Authentication required'}), 401
            
            if not g.current_user.has_permission(permission):
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


class LicenseValidator:
    """License validation utilities"""
    
    @staticmethod
    def generate_signature(license_key, hwid, timestamp):
        """Generate HMAC signature for license validation"""
        message = f"{license_key}:{hwid}:{timestamp}"
        signature = hashlib.hmac.new(
            current_app.config['LICENSE_SIGNATURE_KEY'].encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    @staticmethod
    def verify_signature(license_key, hwid, timestamp, signature):
        """Verify HMAC signature for license validation"""
        expected_signature = LicenseValidator.generate_signature(license_key, hwid, timestamp)
        return secrets.compare_digest(expected_signature, signature)
    
    @staticmethod
    def is_timestamp_valid(timestamp, window_minutes=2):
        """Check if timestamp is within valid window"""
        now = datetime.utcnow().timestamp()
        window_seconds = window_minutes * 60
        return abs(now - timestamp) <= window_seconds


def get_client_ip():
    """Get client IP address from request"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr


def get_user_agent():
    """Get user agent from request"""
    return request.headers.get('User-Agent', '')


def get_country_from_ip(ip_address):
    """Get country code from IP address (placeholder)"""
    # In production, integrate with GeoIP service
    return 'US'  # Default for development
