/* Glassmorphism Styles */
.glass-surface {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-surface-dark {
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(148, 163, 184, 0.1);
}

.glass-card {
    @apply glass-surface rounded-2xl p-6 transition-all duration-200;
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* <PERSON><PERSON> Styles */
.btn-primary {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.9) 0%, rgba(59, 130, 246, 0.9) 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #475569;
    font-weight: 500;
    padding: 12px 24px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    color: #334155;
    text-decoration: none;
}

.btn-ghost {
    background: transparent;
    color: #64748b;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #475569;
    text-decoration: none;
}

/* Input Styles */
.input-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    color: #1e293b;
    transition: all 0.2s ease;
    width: 100%;
}

.input-glass:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.input-glass::placeholder {
    color: #64748b;
}

/* Navigation Styles */
.nav-link {
    color: #64748b;
    font-weight: 500;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 8px;
}

.nav-link:hover {
    color: #0ea5e9;
    text-decoration: none;
}

.nav-link.active {
    color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.2s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
    margin-bottom: 24px;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.card-description {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 4px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-expired {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-suspended {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.2s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #e2e8f0;
    border-top: 2px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Table Styles */
.table-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.table-glass th {
    background: rgba(255, 255, 255, 0.1);
    color: #475569;
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-glass td {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: #1e293b;
}

.table-glass tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.form-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 4px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .glass-surface {
        background: rgba(15, 23, 42, 0.3);
        border: 1px solid rgba(148, 163, 184, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(148, 163, 184, 0.1);
    }
    
    .card-title {
        color: #f1f5f9;
    }
    
    .input-glass {
        color: #f1f5f9;
    }
    
    .table-glass td {
        color: #f1f5f9;
    }
    
    .form-label {
        color: #d1d5db;
    }
}
