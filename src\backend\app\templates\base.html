<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Pepe Auth - Next-Generation Licensing Platform{% endblock %}</title>
    <meta name="description" content="Secure, scalable, and feature-rich licensing and authentication platform">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    {% block head %}{% endblock %}
</head>
<body class="h-full bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
    
    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="glass-surface border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ url_for('main.dashboard') }}" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">PA</span>
                        </div>
                        <span class="text-xl font-bold text-slate-900 dark:text-white">Pepe Auth</span>
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ url_for('main.dashboard') }}" class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}">
                        <i data-lucide="layout-dashboard" class="w-4 h-4 mr-2"></i>
                        Dashboard
                    </a>
                    <a href="{{ url_for('main.licenses') }}" class="nav-link {% if request.endpoint == 'main.licenses' %}active{% endif %}">
                        <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                        Licenses
                    </a>
                    <a href="{{ url_for('main.analytics') }}" class="nav-link {% if request.endpoint == 'main.analytics' %}active{% endif %}">
                        <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                        Analytics
                    </a>
                    <a href="{{ url_for('api_keys.list_api_keys') }}" class="nav-link {% if request.endpoint.startswith('api_keys.') %}active{% endif %}">
                        <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                        API Keys
                    </a>
                    <a href="{{ url_for('webhooks.list_webhooks') }}" class="nav-link {% if request.endpoint.startswith('webhooks.') %}active{% endif %}">
                        <i data-lucide="webhook" class="w-4 h-4 mr-2"></i>
                        Webhooks
                    </a>
                    <a href="{{ url_for('main.settings') }}" class="nav-link {% if request.endpoint == 'main.settings' %}active{% endif %}">
                        <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                        Settings
                    </a>
                    
                    <!-- User menu -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">{{ current_user.username[0].upper() }}</span>
                            </div>
                            <span class="hidden md:block">{{ current_user.username }}</span>
                        </button>
                    </div>
                    
                    <a href="{{ url_for('auth.logout') }}" class="btn-ghost">
                        <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="fixed top-4 right-4 z-50 space-y-2">
                {% for category, message in messages %}
                    <div class="glass-surface rounded-lg p-4 max-w-sm animate-fade-in
                        {% if category == 'error' %}border-red-500/50 bg-red-50/10{% endif %}
                        {% if category == 'success' %}border-green-500/50 bg-green-50/10{% endif %}
                        {% if category == 'info' %}border-blue-500/50 bg-blue-50/10{% endif %}
                        {% if category == 'warning' %}border-yellow-500/50 bg-yellow-50/10{% endif %}">
                        <div class="flex items-center">
                            {% if category == 'error' %}
                                <i data-lucide="alert-circle" class="w-5 h-5 text-red-500 mr-2"></i>
                            {% elif category == 'success' %}
                                <i data-lucide="check-circle" class="w-5 h-5 text-green-500 mr-2"></i>
                            {% elif category == 'info' %}
                                <i data-lucide="info" class="w-5 h-5 text-blue-500 mr-2"></i>
                            {% elif category == 'warning' %}
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-500 mr-2"></i>
                            {% endif %}
                            <span class="text-slate-900 dark:text-white">{{ message }}</span>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main content -->
    <main class="{% if current_user.is_authenticated %}pt-8{% else %}pt-0{% endif %}">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="mt-16 py-8 border-t border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="text-slate-600 dark:text-slate-400">
                    <p>&copy; 2025 Pepe Auth. Built with ❤️ for developers.</p>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="{{ url_for('docs.index') }}" class="text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        Documentation
                    </a>
                    <a href="{{ url_for('status.status_page') }}" class="text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        Status
                    </a>
                    <a href="mailto:<EMAIL>" class="text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        Support
                    </a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Initialize Lucide icons -->
    <script>
        lucide.createIcons();
        
        // Auto-hide flash messages
        setTimeout(() => {
            const messages = document.querySelectorAll('.animate-fade-in');
            messages.forEach(msg => {
                msg.style.opacity = '0';
                msg.style.transform = 'translateX(100%)';
                setTimeout(() => msg.remove(), 300);
            });
        }, 5000);
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
