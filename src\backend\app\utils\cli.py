"""
CLI Commands
"""
import click
from flask.cli import with_appcontext

from app import db
from app.models.user import User, UserRole
from app.models.plan import Plan, PlanType


def register_cli_commands(app):
    """Register CLI commands for the Flask app"""
    
    @app.cli.command()
    @with_appcontext
    def init_db():
        """Initialize the database with default data"""
        click.echo('Initializing database...')
        
        # Create tables
        db.create_all()
        
        # Create default plans if they don't exist
        plans_data = [
            {
                'name': 'Free Trial',
                'type': PlanType.FREE_TRIAL,
                'description': '30-day free trial with basic features',
                'price_monthly': 0.00,
                'price_yearly': 0.00,
                'max_licenses': 1,
                'max_devices_per_license': 1,
                'max_api_calls_per_month': 1000
            },
            {
                'name': 'Hobby',
                'type': PlanType.HOBBY,
                'description': 'Perfect for individual developers and small projects',
                'price_monthly': 9.99,
                'price_yearly': 99.99,
                'max_licenses': 5,
                'max_devices_per_license': 2,
                'max_api_calls_per_month': 10000
            },
            {
                'name': 'Pro',
                'type': PlanType.PRO,
                'description': 'Advanced features for growing businesses',
                'price_monthly': 29.99,
                'price_yearly': 299.99,
                'max_licenses': 25,
                'max_devices_per_license': 5,
                'max_api_calls_per_month': 100000
            },
            {
                'name': 'Enterprise',
                'type': PlanType.ENTERPRISE,
                'description': 'Full-featured solution for large organizations',
                'price_monthly': 99.99,
                'price_yearly': 999.99,
                'max_licenses': -1,  # Unlimited
                'max_devices_per_license': -1,  # Unlimited
                'max_api_calls_per_month': -1  # Unlimited
            }
        ]
        
        for plan_data in plans_data:
            existing_plan = Plan.query.filter_by(type=plan_data['type']).first()
            if not existing_plan:
                plan = Plan(**plan_data)
                plan.save()
                click.echo(f'Created plan: {plan.name}')
        
        click.echo('Database initialized successfully!')
    
    @app.cli.command()
    @click.option('--email', prompt=True, help='Admin email')
    @click.option('--username', prompt=True, help='Admin username')
    @click.option('--password', prompt=True, hide_input=True, help='Admin password')
    @with_appcontext
    def create_admin(email, username, password):
        """Create an admin user"""
        # Check if user already exists
        if User.get_by_email(email):
            click.echo(f'User with email {email} already exists!')
            return
        
        if User.get_by_username(username):
            click.echo(f'User with username {username} already exists!')
            return
        
        # Create admin user
        admin = User(
            email=email.lower(),
            username=username.lower(),
            password=password,
            first_name='Admin',
            last_name='User',
            role=UserRole.OWNER,
            is_active=True,
            is_verified=True
        )
        admin.save()
        
        click.echo(f'Admin user created successfully: {email}')
    
    @app.cli.command()
    @with_appcontext
    def reset_db():
        """Reset the database (WARNING: This will delete all data!)"""
        if click.confirm('This will delete all data. Are you sure?'):
            db.drop_all()
            db.create_all()
            click.echo('Database reset successfully!')
        else:
            click.echo('Database reset cancelled.')
    
    @app.cli.command()
    @with_appcontext
    def seed_data():
        """Seed the database with sample data"""
        click.echo('Seeding database with sample data...')

        from app.models.license import License
        from app.models.user import User

        # Get admin user
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            click.echo('Admin user not found. Please create admin user first.')
            return

        # Get free trial plan
        free_plan = Plan.query.filter_by(type=PlanType.FREE_TRIAL).first()
        if not free_plan:
            click.echo('Free trial plan not found.')
            return

        # Create sample licenses
        from datetime import datetime, timedelta

        for i in range(5):
            expires_at = None
            if i == 3:  # One expired license
                expires_at = datetime.utcnow() - timedelta(days=5)
            elif i == 4:  # One expiring soon
                expires_at = datetime.utcnow() + timedelta(days=3)

            license_obj = License(
                user_id=admin.id,
                plan_id=free_plan.id,
                max_devices=2 if i > 0 else 1,
                expires_at=expires_at
            )
            license_obj.save()
            click.echo(f'Created sample license: {license_obj.license_key}')

            # Create some sample validations
            from app.models.license import LicenseValidation, ValidationResult
            import random

            for j in range(random.randint(5, 15)):
                validation = LicenseValidation(
                    license_id=license_obj.id,
                    license_key=license_obj.license_key,
                    device_fingerprint=f"device_{j}_{i}",
                    ip_address=f"192.168.1.{random.randint(100, 200)}",
                    user_agent="Demo User Agent",
                    result=ValidationResult.SUCCESS if random.random() > 0.1 else ValidationResult.INVALID_KEY,
                    response_time_ms=random.randint(20, 100),
                    created_at=datetime.utcnow() - timedelta(hours=random.randint(1, 72))
                )
                validation.save()

        click.echo('Database seeded successfully with demo data!')

    @app.cli.command()
    @with_appcontext
    def maintenance():
        """Run maintenance tasks"""
        click.echo('Running maintenance tasks...')

        from app.utils.maintenance import run_maintenance_tasks
        results = run_maintenance_tasks()

        for task, result in results.items():
            click.echo(f'{task}: {result}')

        click.echo('Maintenance completed!')

    @app.cli.command()
    @with_appcontext
    def backup():
        """Create database backup"""
        click.echo('Creating database backup...')

        from app.utils.maintenance import BackupManager
        backup_manager = BackupManager()

        try:
            backup_file = backup_manager.create_backup()
            click.echo(f'Backup created: {backup_file}')
        except Exception as e:
            click.echo(f'Backup failed: {e}')

    @app.cli.command()
    @with_appcontext
    def health_check():
        """Check system health"""
        from app.utils.maintenance import MaintenanceManager

        health = MaintenanceManager.check_system_health()
        click.echo(f"System Status: {health['status'].upper()}")

        for check, result in health['checks'].items():
            click.echo(f"  {check}: {result}")

    @app.cli.command()
    @with_appcontext
    def system_report():
        """Generate system report"""
        from app.utils.maintenance import MaintenanceManager
        import json

        report = MaintenanceManager.generate_system_report()
        click.echo(json.dumps(report, indent=2, default=str))
