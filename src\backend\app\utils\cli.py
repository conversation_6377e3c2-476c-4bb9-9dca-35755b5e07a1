"""
CLI Commands
"""
import click
from flask.cli import with_appcontext

from app import db
from app.models.user import User, UserRole
from app.models.plan import Plan, PlanType


def register_cli_commands(app):
    """Register CLI commands for the Flask app"""
    
    @app.cli.command()
    @with_appcontext
    def init_db():
        """Initialize the database with default data"""
        click.echo('Initializing database...')
        
        # Create tables
        db.create_all()
        
        # Create default plans if they don't exist
        plans_data = [
            {
                'name': 'Free Trial',
                'type': PlanType.FREE_TRIAL,
                'description': '30-day free trial with basic features',
                'price_monthly': 0.00,
                'price_yearly': 0.00,
                'max_licenses': 1,
                'max_devices_per_license': 1,
                'max_api_calls_per_month': 1000
            },
            {
                'name': 'Hobby',
                'type': PlanType.HOBBY,
                'description': 'Perfect for individual developers and small projects',
                'price_monthly': 9.99,
                'price_yearly': 99.99,
                'max_licenses': 5,
                'max_devices_per_license': 2,
                'max_api_calls_per_month': 10000
            },
            {
                'name': 'Pro',
                'type': PlanType.PRO,
                'description': 'Advanced features for growing businesses',
                'price_monthly': 29.99,
                'price_yearly': 299.99,
                'max_licenses': 25,
                'max_devices_per_license': 5,
                'max_api_calls_per_month': 100000
            },
            {
                'name': 'Enterprise',
                'type': PlanType.ENTERPRISE,
                'description': 'Full-featured solution for large organizations',
                'price_monthly': 99.99,
                'price_yearly': 999.99,
                'max_licenses': -1,  # Unlimited
                'max_devices_per_license': -1,  # Unlimited
                'max_api_calls_per_month': -1  # Unlimited
            }
        ]
        
        for plan_data in plans_data:
            existing_plan = Plan.query.filter_by(type=plan_data['type']).first()
            if not existing_plan:
                plan = Plan(**plan_data)
                plan.save()
                click.echo(f'Created plan: {plan.name}')
        
        click.echo('Database initialized successfully!')
    
    @app.cli.command()
    @click.option('--email', prompt=True, help='Admin email')
    @click.option('--username', prompt=True, help='Admin username')
    @click.option('--password', prompt=True, hide_input=True, help='Admin password')
    @with_appcontext
    def create_admin(email, username, password):
        """Create an admin user"""
        # Check if user already exists
        if User.get_by_email(email):
            click.echo(f'User with email {email} already exists!')
            return
        
        if User.get_by_username(username):
            click.echo(f'User with username {username} already exists!')
            return
        
        # Create admin user
        admin = User(
            email=email.lower(),
            username=username.lower(),
            password=password,
            first_name='Admin',
            last_name='User',
            role=UserRole.OWNER,
            is_active=True,
            is_verified=True
        )
        admin.save()
        
        click.echo(f'Admin user created successfully: {email}')
    
    @app.cli.command()
    @with_appcontext
    def reset_db():
        """Reset the database (WARNING: This will delete all data!)"""
        if click.confirm('This will delete all data. Are you sure?'):
            db.drop_all()
            db.create_all()
            click.echo('Database reset successfully!')
        else:
            click.echo('Database reset cancelled.')
    
    @app.cli.command()
    @with_appcontext
    def seed_data():
        """Seed the database with sample data"""
        click.echo('Seeding database with sample data...')
        
        # This would contain sample data creation
        # For now, just initialize the database
        init_db.callback()
        
        click.echo('Database seeded successfully!')
