{% extends "base.html" %}

{% block title %}Page Not Found - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="w-24 h-24 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-8">
            <i data-lucide="search-x" class="w-12 h-12 text-white"></i>
        </div>

        <!-- Error Message -->
        <h1 class="text-6xl font-bold text-slate-900 dark:text-white mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
            Page Not Found
        </h2>
        <p class="text-slate-600 dark:text-slate-400 mb-8">
            The page you're looking for doesn't exist or has been moved.
        </p>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <a href="{{ url_for('main.index') }}" class="btn-primary w-full">
                <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                Go Home
            </a>
            <button onclick="history.back()" class="btn-secondary w-full">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                Go Back
            </button>
        </div>

        <!-- Helpful Links -->
        <div class="mt-8 pt-8 border-t border-white/10">
            <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                Looking for something specific?
            </p>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <a href="{{ url_for('main.dashboard') if current_user.is_authenticated else url_for('auth.login') }}" 
                   class="text-primary-600 hover:text-primary-500">
                    Dashboard
                </a>
                <a href="{{ url_for('docs.index') }}" class="text-primary-600 hover:text-primary-500">
                    Documentation
                </a>
                <a href="{{ url_for('main.plans') }}" class="text-primary-600 hover:text-primary-500">
                    Pricing
                </a>
                <a href="{{ url_for('status.status_page') }}" class="text-primary-600 hover:text-primary-500">
                    Status
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
