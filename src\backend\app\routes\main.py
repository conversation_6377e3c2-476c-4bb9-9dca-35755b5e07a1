"""
Main Web Routes with Flask Templates
"""
from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
import structlog

from app.models.license import License, LicenseValidation, ValidationResult
from app.models.plan import Plan

logger = structlog.get_logger()

main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """Landing page"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')


@main_bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard"""
    try:
        # Get user's licenses
        licenses = current_user.licenses.limit(5).all()
        
        # Get basic stats
        total_licenses = current_user.licenses.count()
        active_licenses = current_user.licenses.filter_by(status='active').count()
        
        # Get recent validations
        recent_validations = LicenseValidation.query.join(License).filter(
            License.user_id == current_user.id
        ).order_by(LicenseValidation.created_at.desc()).limit(10).all()
        
        return render_template('dashboard.html', 
                             licenses=licenses,
                             total_licenses=total_licenses,
                             active_licenses=active_licenses,
                             recent_validations=recent_validations)
    except Exception as e:
        logger.error("Dashboard error", error=str(e))
        flash('Error loading dashboard', 'error')
        return render_template('dashboard.html', 
                             licenses=[], 
                             total_licenses=0, 
                             active_licenses=0,
                             recent_validations=[])


@main_bp.route('/licenses')
@login_required
def licenses():
    """Licenses page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        licenses_query = current_user.licenses.order_by(License.created_at.desc())
        licenses_pagination = licenses_query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return render_template('licenses.html', 
                             licenses=licenses_pagination.items,
                             pagination=licenses_pagination)
    except Exception as e:
        logger.error("Licenses page error", error=str(e))
        flash('Error loading licenses', 'error')
        return render_template('licenses.html', licenses=[], pagination=None)


@main_bp.route('/analytics')
@login_required
def analytics():
    """Analytics page"""
    try:
        # Get validation stats
        total_validations = LicenseValidation.query.join(License).filter(
            License.user_id == current_user.id
        ).count()
        
        successful_validations = LicenseValidation.query.join(License).filter(
            License.user_id == current_user.id,
            LicenseValidation.result == ValidationResult.SUCCESS
        ).count()
        
        success_rate = (successful_validations / total_validations * 100) if total_validations > 0 else 0
        
        return render_template('analytics.html',
                             total_validations=total_validations,
                             successful_validations=successful_validations,
                             success_rate=round(success_rate, 2))
    except Exception as e:
        logger.error("Analytics page error", error=str(e))
        flash('Error loading analytics', 'error')
        return render_template('analytics.html',
                             total_validations=0,
                             successful_validations=0,
                             success_rate=0)


@main_bp.route('/settings')
@login_required
def settings():
    """Settings page"""
    return render_template('settings.html')


@main_bp.route('/plans')
def plans():
    """Plans page"""
    try:
        plans = Plan.get_active_plans()
        return render_template('plans.html', plans=plans)
    except Exception as e:
        logger.error("Plans page error", error=str(e))
        flash('Error loading plans', 'error')
        return render_template('plans.html', plans=[])
