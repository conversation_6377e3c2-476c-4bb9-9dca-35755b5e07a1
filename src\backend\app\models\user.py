"""
User Model
"""
from datetime import datetime, timed<PERSON>ta
from flask_login import UserMixin
from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, Integer, DateTime, Enum

from sqlalchemy.orm import relationship
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
import pyotp
import enum

from app import db
from .base import BaseModel


class UserRole(enum.Enum):
    """User role enumeration"""
    OWNER = 'owner'
    ADMIN = 'admin'
    BILLING = 'billing'
    SUPPORT = 'support'
    READONLY = 'readonly'


class User(BaseModel, UserMixin):
    """User model for authentication and authorization"""
    
    __tablename__ = 'users'
    
    # Basic information
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    
    # Role and status
    role = Column(Enum(UserRole), default=UserRole.READONLY, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Two-factor authentication
    totp_secret = Column(String(32))
    totp_enabled = Column(Boolean, default=False, nullable=False)
    
    # Login tracking
    last_login_at = Column(DateTime)
    last_login_ip = Column(String(45))  # IPv6 compatible
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime)
    
    # Relationships
    licenses = relationship('License', back_populates='user', lazy='dynamic')
    api_keys = relationship('ApiKey', back_populates='user', lazy='dynamic')
    audit_logs = relationship('AuditLog', back_populates='user', lazy='dynamic')
    webhooks = relationship('Webhook', back_populates='user', lazy='dynamic')
    referrals_made = relationship('Referral', foreign_keys='Referral.referrer_id', back_populates='referrer', lazy='dynamic')
    referrals_received = relationship('Referral', foreign_keys='Referral.referee_id', back_populates='referee', lazy='dynamic')
    giveaways = relationship('Giveaway', back_populates='user', lazy='dynamic')
    
    def __init__(self, **kwargs):
        """Initialize user with password hashing"""
        password = kwargs.pop('password', None)
        super().__init__(**kwargs)
        if password:
            self.set_password(password)
    
    def set_password(self, password):
        """Hash and set password"""
        ph = PasswordHasher()
        self.password_hash = ph.hash(password)
    
    def check_password(self, password):
        """Verify password"""
        ph = PasswordHasher()
        try:
            ph.verify(self.password_hash, password)
            return True
        except VerifyMismatchError:
            return False
    
    def generate_totp_secret(self):
        """Generate TOTP secret for 2FA"""
        self.totp_secret = pyotp.random_base32()
        return self.totp_secret
    
    def get_totp_uri(self, issuer_name='Pepe Auth'):
        """Get TOTP URI for QR code generation"""
        if not self.totp_secret:
            self.generate_totp_secret()
        
        return pyotp.totp.TOTP(self.totp_secret).provisioning_uri(
            name=self.email,
            issuer_name=issuer_name
        )
    
    def verify_totp(self, token):
        """Verify TOTP token"""
        if not self.totp_secret:
            return False
        
        totp = pyotp.TOTP(self.totp_secret)
        return totp.verify(token, valid_window=1)
    
    def enable_2fa(self, token):
        """Enable 2FA after verifying token"""
        if self.verify_totp(token):
            self.totp_enabled = True
            return True
        return False
    
    def disable_2fa(self):
        """Disable 2FA"""
        self.totp_enabled = False
        self.totp_secret = None
    
    def is_locked(self):
        """Check if account is locked"""
        if self.locked_until and self.locked_until > datetime.utcnow():
            return True
        return False
    
    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0
    
    def unlock_account(self):
        """Unlock account"""
        self.locked_until = None
        self.failed_login_attempts = 0
    
    def increment_failed_login(self, max_attempts=5):
        """Increment failed login attempts and lock if necessary"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= max_attempts:
            self.lock_account()
    
    def record_successful_login(self, ip_address=None):
        """Record successful login"""
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip_address
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def has_role(self, role):
        """Check if user has specific role"""
        if isinstance(role, str):
            role = UserRole(role)
        return self.role == role
    
    def has_permission(self, permission):
        """Check if user has specific permission based on role"""
        role_permissions = {
            UserRole.OWNER: ['all'],
            UserRole.ADMIN: ['manage_users', 'manage_licenses', 'view_analytics', 'manage_settings'],
            UserRole.BILLING: ['manage_billing', 'view_analytics'],
            UserRole.SUPPORT: ['view_users', 'view_licenses', 'manage_support'],
            UserRole.READONLY: ['view_own_data']
        }
        
        user_permissions = role_permissions.get(self.role, [])
        return 'all' in user_permissions or permission in user_permissions
    
    @property
    def full_name(self):
        """Get full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username
    
    @property
    def is_admin(self):
        """Check if user is admin or owner"""
        return self.role in [UserRole.OWNER, UserRole.ADMIN]
    
    def to_dict(self, exclude=None):
        """Convert to dictionary, excluding sensitive fields"""
        exclude = exclude or ['password_hash', 'totp_secret']
        data = super().to_dict(exclude=exclude)
        data['role'] = self.role.value if self.role else None
        data['full_name'] = self.full_name
        data['is_admin'] = self.is_admin
        return data
    
    @classmethod
    def get_by_email(cls, email):
        """Get user by email"""
        return cls.query.filter_by(email=email.lower()).first()
    
    @classmethod
    def get_by_username(cls, username):
        """Get user by username"""
        return cls.query.filter_by(username=username.lower()).first()
    
    @classmethod
    def get_by_login(cls, login):
        """Get user by email or username"""
        login = login.lower()
        return cls.query.filter(
            (cls.email == login) | (cls.username == login)
        ).first()
    
    def __repr__(self):
        return f'<User {self.username}>'
