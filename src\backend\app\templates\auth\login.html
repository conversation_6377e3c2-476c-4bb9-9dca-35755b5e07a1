{% extends "base.html" %}

{% block title %}Sign In - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <span class="text-white font-bold text-xl">PA</span>
                </div>
            </div>
            <h2 class="text-3xl font-bold text-slate-900 dark:text-white">
                Welcome back
            </h2>
            <p class="mt-2 text-sm text-slate-600 dark:text-slate-400">
                Sign in to your account to continue
            </p>
        </div>

        <!-- Login Form -->
        <div class="card">
            <form method="POST" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <div class="form-group">
                    {{ form.login.label(class="form-label") }}
                    {{ form.login(class="input-glass", placeholder="Enter your email or username") }}
                    {% if form.login.errors %}
                        <div class="form-error">
                            {% for error in form.login.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="input-glass", placeholder="Enter your password") }}
                    {% if form.password.errors %}
                        <div class="form-error">
                            {% for error in form.password.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        {{ form.remember_me(class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600") }}
                        {{ form.remember_me.label(class="ml-2 text-sm text-slate-600 dark:text-slate-400") }}
                    </div>
                    <div class="text-sm">
                        <a href="#" class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <div>
                    {{ form.submit(class="btn-primary w-full justify-center") }}
                </div>
            </form>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-white/20"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white/10 text-slate-600 dark:text-slate-400">
                            Don't have an account?
                        </span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ url_for('auth.register') }}" class="btn-secondary w-full justify-center">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Create Account
                    </a>
                </div>
            </div>
        </div>

        <!-- Back to home -->
        <div class="text-center">
            <a href="{{ url_for('main.index') }}" class="text-sm text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-1 inline"></i>
                Back to home
            </a>
        </div>
    </div>
</div>
{% endblock %}
