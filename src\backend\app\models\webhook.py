"""
Webhook Models
"""
import secrets
from datetime import datetime
from sqlalchemy import Column, String, Text, <PERSON><PERSON><PERSON>, Integer, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class Webhook(BaseModel):
    """Webhook model for event notifications"""
    
    __tablename__ = 'webhooks'
    
    # Relationships
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    
    # Webhook details
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    secret = Column(String(255), nullable=False)
    events = Column(JSON, nullable=False)  # Store as JSON array for SQLite
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    last_triggered_at = Column(DateTime)
    failure_count = Column(Integer, default=0, nullable=False)
    
    # Relationships
    user = relationship('User', back_populates='webhooks')
    deliveries = relationship('WebhookDelivery', back_populates='webhook', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        """Initialize webhook with auto-generated secret"""
        if 'secret' not in kwargs:
            kwargs['secret'] = secrets.token_urlsafe(32)
        super().__init__(**kwargs)
    
    def __repr__(self):
        return f'<Webhook {self.name}>'


class WebhookDelivery(BaseModel):
    """Webhook delivery attempt record"""
    
    __tablename__ = 'webhook_deliveries'
    
    # Relationships
    webhook_id = Column(String(36), ForeignKey('webhooks.id'), nullable=False)
    
    # Delivery details
    event_type = Column(String(50), nullable=False)
    payload = Column(JSON, nullable=False)
    response_status = Column(Integer)
    response_body = Column(Text)
    delivery_attempts = Column(Integer, default=1, nullable=False)
    delivered_at = Column(DateTime)
    
    # Relationships
    webhook = relationship('Webhook', back_populates='deliveries')
    
    def __repr__(self):
        return f'<WebhookDelivery {self.event_type}>'
