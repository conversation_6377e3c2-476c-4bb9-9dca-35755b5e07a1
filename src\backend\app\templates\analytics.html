{% extends "base.html" %}

{% block title %}Analytics - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Analytics</h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            Monitor your license usage and validation performance
        </p>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="activity" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Total Validations</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ total_validations }}</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Successful</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ successful_validations }}</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Success Rate</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ success_rate }}%</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Avg Response</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">45ms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Validation Trends -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Validation Trends</h3>
                <p class="card-description">License validation requests over time</p>
            </div>
            
            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 rounded-lg">
                <div class="text-center">
                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                    <p class="text-slate-600 dark:text-slate-400">Chart visualization coming soon</p>
                </div>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Geographic Distribution</h3>
                <p class="card-description">Validation requests by country</p>
            </div>
            
            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 rounded-lg">
                <div class="text-center">
                    <i data-lucide="globe" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                    <p class="text-slate-600 dark:text-slate-400">World map visualization coming soon</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Analysis -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Error Analysis</h3>
            <p class="card-description">Breakdown of validation failures</p>
        </div>
        
        {% if total_validations > 0 %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-red-800 dark:text-red-200">Invalid Keys</p>
                            <p class="text-2xl font-bold text-red-900 dark:text-red-100">{{ (total_validations - successful_validations) // 2 }}</p>
                        </div>
                        <i data-lucide="x-circle" class="w-8 h-8 text-red-500"></i>
                    </div>
                </div>

                <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Expired</p>
                            <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{{ (total_validations - successful_validations) // 4 }}</p>
                        </div>
                        <i data-lucide="clock" class="w-8 h-8 text-yellow-500"></i>
                    </div>
                </div>

                <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-orange-800 dark:text-orange-200">HWID Mismatch</p>
                            <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">{{ (total_validations - successful_validations) // 6 }}</p>
                        </div>
                        <i data-lucide="smartphone" class="w-8 h-8 text-orange-500"></i>
                    </div>
                </div>

                <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-800 dark:text-purple-200">Rate Limited</p>
                            <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">{{ (total_validations - successful_validations) // 8 }}</p>
                        </div>
                        <i data-lucide="shield" class="w-8 h-8 text-purple-500"></i>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center py-8">
                <i data-lucide="bar-chart-3" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                <p class="text-slate-600 dark:text-slate-400">No validation data available yet</p>
            </div>
        {% endif %}
    </div>

    <!-- Real-time Activity -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Real-time Activity</h3>
                <p class="card-description">Live validation requests</p>
            </div>
            
            <div class="space-y-3">
                {% for i in range(5) %}
                    <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <div>
                                <p class="font-medium text-slate-900 dark:text-white">License validation</p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">192.168.1.{{ 100 + i }} • Just now</p>
                            </div>
                        </div>
                        <span class="status-badge status-active">Success</span>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
