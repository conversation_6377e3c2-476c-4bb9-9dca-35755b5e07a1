{% extends "base.html" %}

{% block title %}Dashboard - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">
            Welcome back, {{ current_user.first_name or current_user.username }}!
        </h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            Here's what's happening with your licenses today.
        </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="key" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Total Licenses</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ total_licenses }}</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Active Licenses</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ active_licenses }}</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="activity" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Validations Today</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ recent_validations|length }}</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600 dark:text-slate-400">Success Rate</p>
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">98%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-slate-900 dark:text-white mb-4">Quick Actions</h2>
        <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('main.licenses') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create License
            </a>
            <a href="{{ url_for('main.analytics') }}" class="btn-secondary">
                <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                View Analytics
            </a>
            <a href="{{ url_for('main.settings') }}" class="btn-secondary">
                <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                Settings
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Licenses -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Licenses</h3>
                <p class="card-description">Your latest license keys</p>
            </div>
            
            {% if licenses %}
                <div class="space-y-4">
                    {% for license in licenses %}
                        <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                            <div>
                                <p class="font-medium text-slate-900 dark:text-white">
                                    {{ license.license_key[:8] }}...{{ license.license_key[-4:] }}
                                </p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ license.plan.name }} • Created {{ license.created_at.strftime('%b %d, %Y') }}
                                </p>
                            </div>
                            <div>
                                <span class="status-badge status-{{ license.status.value }}">
                                    {{ license.status.value.title() }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <div class="mt-6">
                    <a href="{{ url_for('main.licenses') }}" class="btn-ghost">
                        View all licenses
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                    </a>
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i data-lucide="key" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                    <p class="text-slate-600 dark:text-slate-400 mb-4">No licenses yet</p>
                    <a href="{{ url_for('main.licenses') }}" class="btn-primary">
                        Create your first license
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Recent Validations -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Validations</h3>
                <p class="card-description">Latest license validation attempts</p>
            </div>
            
            {% if recent_validations %}
                <div class="space-y-4">
                    {% for validation in recent_validations %}
                        <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                            <div>
                                <p class="font-medium text-slate-900 dark:text-white">
                                    {{ validation.license_key[:8] }}...{{ validation.license_key[-4:] }}
                                </p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ validation.ip_address or 'Unknown IP' }} • {{ validation.created_at.strftime('%H:%M') }}
                                </p>
                            </div>
                            <div>
                                <span class="status-badge {% if validation.result.value == 'success' %}status-active{% else %}status-expired{% endif %}">
                                    {{ validation.result.value.title() }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <div class="mt-6">
                    <a href="{{ url_for('main.analytics') }}" class="btn-ghost">
                        View all validations
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                    </a>
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i data-lucide="activity" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                    <p class="text-slate-600 dark:text-slate-400">No validations yet</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
