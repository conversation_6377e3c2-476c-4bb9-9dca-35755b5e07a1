# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pepe_auth
DEV_DATABASE_URL=postgresql://postgres:password@localhost:5432/pepe_auth_dev
TEST_DATABASE_URL=sqlite:///:memory:

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
TEST_REDIS_URL=redis://localhost:6379/1

# Security Configuration
LICENSE_SIGNATURE_KEY=your-license-signature-key-here
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Mail Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_2FA=true
ENABLE_EMAIL_VERIFICATION=true

# External Services
SENTRY_DSN=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# File Upload
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Logging
LOG_LEVEL=INFO
