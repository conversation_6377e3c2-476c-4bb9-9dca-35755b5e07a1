{% extends "base.html" %}

{% block title %}Settings - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Settings</h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            Manage your account and application preferences
        </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Settings Navigation -->
        <div class="lg:col-span-1">
            <nav class="space-y-2">
                <a href="#profile" class="nav-link active block">
                    <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                    Profile
                </a>
                <a href="#security" class="nav-link block">
                    <i data-lucide="shield" class="w-4 h-4 mr-2"></i>
                    Security
                </a>
                <a href="#api" class="nav-link block">
                    <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                    API Keys
                </a>
                <a href="#notifications" class="nav-link block">
                    <i data-lucide="bell" class="w-4 h-4 mr-2"></i>
                    Notifications
                </a>
                <a href="#billing" class="nav-link block">
                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                    Billing
                </a>
            </nav>
        </div>

        <!-- Settings Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Profile Settings -->
            <div id="profile" class="card">
                <div class="card-header">
                    <h3 class="card-title">Profile Information</h3>
                    <p class="card-description">Update your account profile information</p>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="form-label">First Name</label>
                            <input type="text" class="input-glass" value="{{ current_user.first_name or '' }}" placeholder="First name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Last Name</label>
                            <input type="text" class="input-glass" value="{{ current_user.last_name or '' }}" placeholder="Last name">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="input-glass" value="{{ current_user.email }}" readonly>
                        <p class="text-sm text-slate-500 mt-1">Email cannot be changed</p>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Username</label>
                        <input type="text" class="input-glass" value="{{ current_user.username }}" readonly>
                        <p class="text-sm text-slate-500 mt-1">Username cannot be changed</p>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="button" class="btn-primary" onclick="alert('Profile update functionality coming soon!')">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>

            <!-- Security Settings -->
            <div id="security" class="card">
                <div class="card-header">
                    <h3 class="card-title">Security</h3>
                    <p class="card-description">Manage your account security settings</p>
                </div>
                
                <div class="space-y-6">
                    <!-- Change Password -->
                    <div class="border-b border-white/10 pb-6">
                        <h4 class="font-medium text-slate-900 dark:text-white mb-4">Change Password</h4>
                        <form class="space-y-4">
                            <div class="form-group">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="input-glass" placeholder="Enter current password">
                            </div>
                            <div class="form-group">
                                <label class="form-label">New Password</label>
                                <input type="password" class="input-glass" placeholder="Enter new password">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="input-glass" placeholder="Confirm new password">
                            </div>
                            <button type="button" class="btn-secondary" onclick="alert('Password change functionality coming soon!')">
                                Update Password
                            </button>
                        </form>
                    </div>

                    <!-- Two-Factor Authentication -->
                    <div>
                        <h4 class="font-medium text-slate-900 dark:text-white mb-4">Two-Factor Authentication</h4>
                        <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                            <div>
                                <p class="font-medium text-slate-900 dark:text-white">TOTP Authentication</p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {% if current_user.totp_enabled %}
                                        Two-factor authentication is enabled
                                    {% else %}
                                        Add an extra layer of security to your account
                                    {% endif %}
                                </p>
                            </div>
                            <div>
                                {% if current_user.totp_enabled %}
                                    <button class="btn-secondary" onclick="alert('2FA disable functionality coming soon!')">
                                        Disable
                                    </button>
                                {% else %}
                                    <button class="btn-primary" onclick="alert('2FA setup functionality coming soon!')">
                                        Enable
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Keys -->
            <div id="api" class="card">
                <div class="card-header">
                    <h3 class="card-title">API Keys</h3>
                    <p class="card-description">Manage your API keys for programmatic access</p>
                </div>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <p class="text-sm text-slate-600 dark:text-slate-400">
                            Manage your API keys for programmatic access
                        </p>
                        <a href="{{ url_for('api_keys.list_api_keys') }}" class="btn-primary">
                            <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                            Manage API Keys
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <div id="notifications" class="card">
                <div class="card-header">
                    <h3 class="card-title">Notifications</h3>
                    <p class="card-description">Configure your notification preferences</p>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-slate-900 dark:text-white">Email Notifications</p>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Receive email alerts for important events</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-slate-900 dark:text-white">License Expiry Alerts</p>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Get notified when licenses are about to expire</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-slate-900 dark:text-white">Security Alerts</p>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Receive alerts for suspicious activity</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Billing -->
            <div id="billing" class="card">
                <div class="card-header">
                    <h3 class="card-title">Billing & Subscription</h3>
                    <p class="card-description">Manage your subscription and billing information</p>
                </div>
                
                <div class="space-y-6">
                    <div class="p-4 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-lg border border-primary-200 dark:border-primary-800">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium text-primary-900 dark:text-primary-100">Free Trial</p>
                                <p class="text-sm text-primary-700 dark:text-primary-300">You're currently on the free trial</p>
                            </div>
                            <button class="btn-primary" onclick="alert('Upgrade functionality coming soon!')">
                                Upgrade Plan
                            </button>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-medium text-slate-900 dark:text-white mb-4">Usage This Month</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-slate-600 dark:text-slate-400">Licenses</span>
                                <span class="text-sm font-medium">0 / 1</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-slate-600 dark:text-slate-400">API Calls</span>
                                <span class="text-sm font-medium">0 / 1,000</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-slate-600 dark:text-slate-400">Devices</span>
                                <span class="text-sm font-medium">0 / 1</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
