{% extends "base.html" %}

{% block title %}Documentation - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Documentation
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Everything you need to integrate Pepe Auth into your applications
        </p>
    </div>

    <!-- Quick Start -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Quick Start</h2>
                    <p class="card-description">Get up and running in minutes</p>
                </div>
                
                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-primary-600 dark:text-primary-400 font-bold text-sm">1</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-slate-900 dark:text-white mb-1">Create an Account</h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Sign up for a free account and get instant access to the dashboard.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-primary-600 dark:text-primary-400 font-bold text-sm">2</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-slate-900 dark:text-white mb-1">Generate API Key</h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Create an API key with the permissions you need for your application.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-primary-600 dark:text-primary-400 font-bold text-sm">3</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-slate-900 dark:text-white mb-1">Create Licenses</h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Generate license keys for your software products.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-primary-600 dark:text-primary-400 font-bold text-sm">4</span>
                        </div>
                        <div>
                            <h3 class="font-medium text-slate-900 dark:text-white mb-1">Integrate & Validate</h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Use our API to validate licenses in your application.</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-white/10">
                    <a href="{{ url_for('docs.getting_started') }}" class="btn-primary">
                        <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                        View Getting Started Guide
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="space-y-6">
            <div class="card">
                <h3 class="font-medium text-slate-900 dark:text-white mb-4">Popular Resources</h3>
                <div class="space-y-3">
                    <a href="{{ url_for('docs.api_reference') }}" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="code" class="w-4 h-4"></i>
                        <span class="text-sm">API Reference</span>
                    </a>
                    <a href="{{ url_for('docs.webhooks') }}" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="webhook" class="w-4 h-4"></i>
                        <span class="text-sm">Webhooks Guide</span>
                    </a>
                    <a href="{{ url_for('docs.sdks') }}" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="package" class="w-4 h-4"></i>
                        <span class="text-sm">SDKs & Libraries</span>
                    </a>
                    <a href="{{ url_for('docs.examples') }}" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="file-text" class="w-4 h-4"></i>
                        <span class="text-sm">Code Examples</span>
                    </a>
                </div>
            </div>

            <div class="card">
                <h3 class="font-medium text-slate-900 dark:text-white mb-4">Need Help?</h3>
                <div class="space-y-3">
                    <a href="mailto:<EMAIL>" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="mail" class="w-4 h-4"></i>
                        <span class="text-sm">Email Support</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="message-circle" class="w-4 h-4"></i>
                        <span class="text-sm">Community Forum</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                        <i data-lucide="github" class="w-4 h-4"></i>
                        <span class="text-sm">GitHub Issues</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Documentation Sections -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="book-open" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Getting Started</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Learn the basics and get your first integration running
                </p>
                <a href="{{ url_for('docs.getting_started') }}" class="btn-secondary">
                    Read Guide
                </a>
            </div>
        </div>

        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="code" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">API Reference</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Complete API documentation with examples
                </p>
                <a href="{{ url_for('docs.api_reference') }}" class="btn-secondary">
                    View API Docs
                </a>
            </div>
        </div>

        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="webhook" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Webhooks</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Real-time notifications for your applications
                </p>
                <a href="{{ url_for('docs.webhooks') }}" class="btn-secondary">
                    Learn More
                </a>
            </div>
        </div>

        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="package" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">SDKs & Libraries</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Official SDKs for popular programming languages
                </p>
                <a href="{{ url_for('docs.sdks') }}" class="btn-secondary">
                    Browse SDKs
                </a>
            </div>
        </div>

        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-red-400 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Code Examples</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Ready-to-use code snippets and examples
                </p>
                <a href="{{ url_for('docs.examples') }}" class="btn-secondary">
                    View Examples
                </a>
            </div>
        </div>

        <div class="card">
            <div class="text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="shield" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Security</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Best practices for secure license validation
                </p>
                <a href="#" class="btn-secondary">
                    Security Guide
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
