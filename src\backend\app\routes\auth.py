"""
Authentication Routes
"""
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, g
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import structlog

from app import db, limiter
from app.models.user import User, UserRole
from app.models.plan import Plan, PlanType
from app.models.audit_log import AuditLog
from app.utils.auth import J<PERSON>TManager, jwt_required, get_client_ip, get_user_agent

logger = structlog.get_logger()

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/register', methods=['POST'])
@limiter.limit("5 per minute")
def register():
    """Register a new user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'username', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Validate email format
        email = data['email'].lower().strip()
        if '@' not in email:
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Validate username
        username = data['username'].lower().strip()
        if len(username) < 3:
            return jsonify({'error': 'Username must be at least 3 characters'}), 400
        
        # Validate password
        password = data['password']
        if len(password) < current_app.config.get('PASSWORD_MIN_LENGTH', 8):
            return jsonify({'error': f'Password must be at least {current_app.config.get("PASSWORD_MIN_LENGTH", 8)} characters'}), 400
        
        # Check if user already exists
        if User.get_by_email(email):
            return jsonify({'error': 'Email already registered'}), 409
        
        if User.get_by_username(username):
            return jsonify({'error': 'Username already taken'}), 409
        
        # Create user
        user = User(
            email=email,
            username=username,
            password=password,
            first_name=data.get('first_name', '').strip(),
            last_name=data.get('last_name', '').strip(),
            role=UserRole.READONLY
        )
        user.save()
        
        # Log registration
        AuditLog.log_action(
            user_id=user.id,
            action='user_registered',
            resource_type='user',
            resource_id=user.id,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        # Generate tokens
        tokens = JWTManager.generate_tokens(user)
        
        logger.info("User registered", user_id=str(user.id), email=email)
        
        return jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict(),
            **tokens
        }), 201
    
    except Exception as e:
        logger.error("Registration failed", error=str(e))
        return jsonify({'error': 'Registration failed'}), 500


@auth_bp.route('/login', methods=['POST'])
@limiter.limit("10 per minute")
def login():
    """Authenticate user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('login') or not data.get('password'):
            return jsonify({'error': 'Login and password are required'}), 400
        
        login_value = data['login'].lower().strip()
        password = data['password']
        totp_code = data.get('totp_code')
        
        # Get user
        user = User.get_by_login(login_value)
        if not user:
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check if account is locked
        if user.is_locked():
            return jsonify({'error': 'Account is temporarily locked'}), 423
        
        # Check if user is active
        if not user.is_active:
            return jsonify({'error': 'Account is disabled'}), 401
        
        # Verify password
        if not user.check_password(password):
            user.increment_failed_login()
            user.save()
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check 2FA if enabled
        if user.totp_enabled:
            if not totp_code:
                return jsonify({'error': '2FA code required', 'requires_2fa': True}), 400
            
            if not user.verify_totp(totp_code):
                return jsonify({'error': 'Invalid 2FA code'}), 401
        
        # Record successful login
        user.record_successful_login(get_client_ip())
        user.save()
        
        # Log login
        AuditLog.log_action(
            user_id=user.id,
            action='user_login',
            resource_type='user',
            resource_id=user.id,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        # Generate tokens
        tokens = JWTManager.generate_tokens(user)
        
        logger.info("User logged in", user_id=str(user.id), email=user.email)
        
        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict(),
            **tokens
        }), 200
    
    except Exception as e:
        logger.error("Login failed", error=str(e))
        return jsonify({'error': 'Login failed'}), 500


@auth_bp.route('/refresh', methods=['POST'])
@limiter.limit("20 per minute")
def refresh():
    """Refresh access token"""
    try:
        data = request.get_json()
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return jsonify({'error': 'Refresh token required'}), 400
        
        # Generate new access token
        tokens = JWTManager.refresh_access_token(refresh_token)
        if not tokens:
            return jsonify({'error': 'Invalid or expired refresh token'}), 401
        
        return jsonify(tokens), 200
    
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        return jsonify({'error': 'Token refresh failed'}), 500


@auth_bp.route('/logout', methods=['POST'])
@jwt_required
def logout():
    """Logout user"""
    try:
        # Log logout
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='user_logout',
            resource_type='user',
            resource_id=g.current_user.id,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        logger.info("User logged out", user_id=str(g.current_user.id))
        
        return jsonify({'message': 'Logout successful'}), 200
    
    except Exception as e:
        logger.error("Logout failed", error=str(e))
        return jsonify({'error': 'Logout failed'}), 500


@auth_bp.route('/me', methods=['GET'])
@jwt_required
def get_current_user():
    """Get current user information"""
    try:
        return jsonify({
            'user': g.current_user.to_dict()
        }), 200
    
    except Exception as e:
        logger.error("Get current user failed", error=str(e))
        return jsonify({'error': 'Failed to get user information'}), 500


@auth_bp.route('/me', methods=['PATCH'])
@jwt_required
def update_current_user():
    """Update current user information"""
    try:
        data = request.get_json()
        
        # Fields that can be updated
        updatable_fields = ['first_name', 'last_name']
        old_values = {}
        new_values = {}
        
        for field in updatable_fields:
            if field in data:
                old_values[field] = getattr(g.current_user, field)
                new_values[field] = data[field]
                setattr(g.current_user, field, data[field])
        
        g.current_user.save()
        
        # Log update
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='user_updated',
            resource_type='user',
            resource_id=g.current_user.id,
            old_values=old_values,
            new_values=new_values,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        return jsonify({
            'message': 'User updated successfully',
            'user': g.current_user.to_dict()
        }), 200
    
    except Exception as e:
        logger.error("Update user failed", error=str(e))
        return jsonify({'error': 'Failed to update user'}), 500


@auth_bp.route('/change-password', methods=['POST'])
@jwt_required
def change_password():
    """Change user password"""
    try:
        data = request.get_json()
        
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        if not current_password or not new_password:
            return jsonify({'error': 'Current and new passwords are required'}), 400
        
        # Verify current password
        if not g.current_user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 400
        
        # Validate new password
        if len(new_password) < current_app.config.get('PASSWORD_MIN_LENGTH', 8):
            return jsonify({'error': f'Password must be at least {current_app.config.get("PASSWORD_MIN_LENGTH", 8)} characters'}), 400
        
        # Update password
        g.current_user.set_password(new_password)
        g.current_user.save()
        
        # Log password change
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='password_changed',
            resource_type='user',
            resource_id=g.current_user.id,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        logger.info("Password changed", user_id=str(g.current_user.id))
        
        return jsonify({'message': 'Password changed successfully'}), 200
    
    except Exception as e:
        logger.error("Password change failed", error=str(e))
        return jsonify({'error': 'Failed to change password'}), 500


@auth_bp.route('/2fa/setup', methods=['POST'])
@jwt_required
def setup_2fa():
    """Setup 2FA for user"""
    try:
        # Generate TOTP secret
        secret = g.current_user.generate_totp_secret()
        g.current_user.save()
        
        # Get QR code URI
        qr_uri = g.current_user.get_totp_uri()
        
        return jsonify({
            'secret': secret,
            'qr_uri': qr_uri,
            'message': 'Scan the QR code with your authenticator app and verify with a code'
        }), 200
    
    except Exception as e:
        logger.error("2FA setup failed", error=str(e))
        return jsonify({'error': 'Failed to setup 2FA'}), 500


@auth_bp.route('/2fa/enable', methods=['POST'])
@jwt_required
def enable_2fa():
    """Enable 2FA after verification"""
    try:
        data = request.get_json()
        totp_code = data.get('totp_code')
        
        if not totp_code:
            return jsonify({'error': 'TOTP code required'}), 400
        
        # Enable 2FA
        if g.current_user.enable_2fa(totp_code):
            g.current_user.save()
            
            # Log 2FA enable
            AuditLog.log_action(
                user_id=g.current_user.id,
                action='2fa_enabled',
                resource_type='user',
                resource_id=g.current_user.id,
                ip_address=get_client_ip(),
                user_agent=get_user_agent()
            )
            
            return jsonify({'message': '2FA enabled successfully'}), 200
        else:
            return jsonify({'error': 'Invalid TOTP code'}), 400
    
    except Exception as e:
        logger.error("2FA enable failed", error=str(e))
        return jsonify({'error': 'Failed to enable 2FA'}), 500


@auth_bp.route('/2fa/disable', methods=['POST'])
@jwt_required
def disable_2fa():
    """Disable 2FA"""
    try:
        data = request.get_json()
        password = data.get('password')
        
        if not password:
            return jsonify({'error': 'Password required to disable 2FA'}), 400
        
        # Verify password
        if not g.current_user.check_password(password):
            return jsonify({'error': 'Invalid password'}), 400
        
        # Disable 2FA
        g.current_user.disable_2fa()
        g.current_user.save()
        
        # Log 2FA disable
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='2fa_disabled',
            resource_type='user',
            resource_id=g.current_user.id,
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        return jsonify({'message': '2FA disabled successfully'}), 200
    
    except Exception as e:
        logger.error("2FA disable failed", error=str(e))
        return jsonify({'error': 'Failed to disable 2FA'}), 500
