"""
Authentication Routes
"""
from datetime import datetime
from flask import Blueprint, request, render_template, redirect, url_for, flash, current_app
from flask_login import login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField, <PERSON>olean<PERSON>ield
from wtforms.validators import DataRequired, Email, Length, EqualTo
import structlog

from app import db
from app.models.user import User, UserRole
from app.models.audit_log import AuditLog

logger = structlog.get_logger()

auth_bp = Blueprint('auth', __name__)


# Forms
class LoginForm(FlaskForm):
    login = StringField('Email or Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')


class RegisterForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    first_name = StringField('First Name', validators=[Length(max=100)])
    last_name = StringField('Last Name', validators=[Length(max=100)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
    password2 = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Create Account')


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Register a new user"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    form = RegisterForm()
    if form.validate_on_submit():
        try:
            # Check if user already exists
            if User.get_by_email(form.email.data.lower()):
                flash('Email already registered', 'error')
                return render_template('auth/register.html', form=form)

            if User.get_by_username(form.username.data.lower()):
                flash('Username already taken', 'error')
                return render_template('auth/register.html', form=form)

            # Create user
            user = User(
                email=form.email.data.lower().strip(),
                username=form.username.data.lower().strip(),
                password=form.password.data,
                first_name=form.first_name.data.strip(),
                last_name=form.last_name.data.strip(),
                role=UserRole.READONLY,
                is_verified=True  # Auto-verify for demo
            )
            user.save()

            # Log registration
            AuditLog.log_action(
                user_id=user.id,
                action='user_registered',
                resource_type='user',
                resource_id=user.id,
                ip_address=request.remote_addr
            )

            flash('Registration successful! You can now sign in.', 'success')
            return redirect(url_for('auth.login'))

        except Exception as e:
            logger.error("Registration failed", error=str(e))
            flash('Registration failed. Please try again.', 'error')

    return render_template('auth/register.html', form=form)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Authenticate user"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        try:
            # Get user
            user = User.get_by_login(form.login.data.lower().strip())
            if not user:
                flash('Invalid credentials', 'error')
                return render_template('auth/login.html', form=form)

            # Check if account is locked
            if user.is_locked():
                flash('Account is temporarily locked', 'error')
                return render_template('auth/login.html', form=form)

            # Check if user is active
            if not user.is_active:
                flash('Account is disabled', 'error')
                return render_template('auth/login.html', form=form)

            # Verify password
            if not user.check_password(form.password.data):
                user.increment_failed_login()
                user.save()
                flash('Invalid credentials', 'error')
                return render_template('auth/login.html', form=form)

            # Record successful login
            user.record_successful_login(request.remote_addr)
            user.save()

            # Log login
            AuditLog.log_action(
                user_id=user.id,
                action='user_login',
                resource_type='user',
                resource_id=user.id,
                ip_address=request.remote_addr
            )

            # Login user
            login_user(user, remember=form.remember_me.data)

            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.dashboard')

            flash('Login successful!', 'success')
            return redirect(next_page)

        except Exception as e:
            logger.error("Login failed", error=str(e))
            flash('Login failed. Please try again.', 'error')

    return render_template('auth/login.html', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """Logout user"""
    try:
        # Log logout
        AuditLog.log_action(
            user_id=current_user.id,
            action='user_logout',
            resource_type='user',
            resource_id=current_user.id,
            ip_address=request.remote_addr
        )

        logout_user()
        flash('You have been logged out.', 'info')
        return redirect(url_for('main.index'))

    except Exception as e:
        logger.error("Logout failed", error=str(e))
        flash('Logout failed.', 'error')
        return redirect(url_for('main.dashboard'))



