{% extends "base.html" %}

{% block title %}Create Account - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <span class="text-white font-bold text-xl">PA</span>
                </div>
            </div>
            <h2 class="text-3xl font-bold text-slate-900 dark:text-white">
                Create your account
            </h2>
            <p class="mt-2 text-sm text-slate-600 dark:text-slate-400">
                Join thousands of developers using Pepe Auth
            </p>
        </div>

        <!-- Register Form -->
        <div class="card">
            <form method="POST" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="form-group">
                        {{ form.first_name.label(class="form-label") }}
                        {{ form.first_name(class="input-glass", placeholder="First name") }}
                        {% if form.first_name.errors %}
                            <div class="form-error">
                                {% for error in form.first_name.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.last_name.label(class="form-label") }}
                        {{ form.last_name(class="input-glass", placeholder="Last name") }}
                        {% if form.last_name.errors %}
                            <div class="form-error">
                                {% for error in form.last_name.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="input-glass", placeholder="Enter your email") }}
                    {% if form.email.errors %}
                        <div class="form-error">
                            {% for error in form.email.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="input-glass", placeholder="Choose a username") }}
                    {% if form.username.errors %}
                        <div class="form-error">
                            {% for error in form.username.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="input-glass", placeholder="Create a password") }}
                    {% if form.password.errors %}
                        <div class="form-error">
                            {% for error in form.password.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.password2.label(class="form-label") }}
                    {{ form.password2(class="input-glass", placeholder="Confirm your password") }}
                    {% if form.password2.errors %}
                        <div class="form-error">
                            {% for error in form.password2.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    {{ form.submit(class="btn-primary w-full justify-center") }}
                </div>
            </form>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-white/20"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white/10 text-slate-600 dark:text-slate-400">
                            Already have an account?
                        </span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ url_for('auth.login') }}" class="btn-secondary w-full justify-center">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        Sign In
                    </a>
                </div>
            </div>
        </div>

        <!-- Back to home -->
        <div class="text-center">
            <a href="{{ url_for('main.index') }}" class="text-sm text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-1 inline"></i>
                Back to home
            </a>
        </div>
    </div>
</div>
{% endblock %}
