{% extends "base.html" %}

{% block title %}Pepe Auth - Next-Generation Licensing Platform{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background gradient -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100"></div>
    
    <!-- Floating elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-primary-200/30 rounded-full blur-xl animate-pulse"></div>
    <div class="absolute top-40 right-20 w-32 h-32 bg-blue-200/30 rounded-full blur-xl animate-pulse" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 left-20 w-24 h-24 bg-indigo-200/30 rounded-full blur-xl animate-pulse" style="animation-delay: 2s;"></div>
    
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in">
            <!-- Logo -->
            <div class="flex justify-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-r from-primary-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <span class="text-white font-bold text-2xl">PA</span>
                </div>
            </div>
            
            <!-- Main heading -->
            <h1 class="text-5xl md:text-7xl font-bold text-slate-900 mb-6">
                Next-Generation
                <span class="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">
                    Licensing
                </span>
            </h1>
            
            <p class="text-xl md:text-2xl text-slate-600 mb-8 max-w-3xl mx-auto">
                Secure, scalable, and feature-rich licensing platform with stunning glassmorphism UI. 
                Built for developers who demand excellence.
            </p>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="{{ url_for('auth.register') }}" class="btn-primary text-lg px-8 py-4">
                    <i data-lucide="rocket" class="w-5 h-5 mr-2"></i>
                    Get Started Free
                </a>
                <a href="{{ url_for('main.plans') }}" class="btn-secondary text-lg px-8 py-4">
                    <i data-lucide="eye" class="w-5 h-5 mr-2"></i>
                    View Plans
                </a>
                <a href="{{ url_for('docs.index') }}" class="btn-secondary text-lg px-8 py-4">
                    <i data-lucide="book-open" class="w-5 h-5 mr-2"></i>
                    Documentation
                </a>
            </div>
            
            <!-- Features grid -->
            <div class="grid md:grid-cols-3 gap-8 mt-16">
                <div class="card animate-scale-in" style="animation-delay: 0.1s;">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-900 mb-2">Ultra Secure</h3>
                        <p class="text-slate-600">Ed25519 signatures, HMAC protection, and offline validation for maximum security.</p>
                    </div>
                </div>
                
                <div class="card animate-scale-in" style="animation-delay: 0.2s;">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-900 mb-2">Lightning Fast</h3>
                        <p class="text-slate-600">Instant setup with SQLite, no complex server configuration required.</p>
                    </div>
                </div>
                
                <div class="card animate-scale-in" style="animation-delay: 0.3s;">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="bar-chart-3" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-900 mb-2">Rich Analytics</h3>
                        <p class="text-slate-600">Real-time dashboards, fraud detection, and comprehensive usage analytics.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<section class="py-20 bg-white/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-slate-900 mb-4">Everything You Need</h2>
            <p class="text-xl text-slate-600 max-w-2xl mx-auto">
                Comprehensive licensing solution with enterprise-grade features
            </p>
        </div>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-red-400 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="key" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="font-semibold text-slate-900 mb-2">License Management</h3>
                <p class="text-slate-600 text-sm">Create, manage, and track licenses with ease</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="smartphone" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="font-semibold text-slate-900 mb-2">Device Tracking</h3>
                <p class="text-slate-600 text-sm">Hardware fingerprinting and device management</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="font-semibold text-slate-900 mb-2">Geofencing</h3>
                <p class="text-slate-600 text-sm">Country-based license restrictions</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-pink-400 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="webhook" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="font-semibold text-slate-900 mb-2">Webhooks</h3>
                <p class="text-slate-600 text-sm">Real-time event notifications</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <div class="card">
            <h2 class="text-3xl font-bold text-slate-900 mb-4">Ready to Get Started?</h2>
            <p class="text-xl text-slate-600 mb-8">
                Join thousands of developers who trust Pepe Auth for their licensing needs
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('auth.register') }}" class="btn-primary text-lg px-8 py-4">
                    Start Free Trial
                </a>
                <a href="{{ url_for('auth.login') }}" class="btn-secondary text-lg px-8 py-4">
                    Sign In
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
