"""
API Key Model
"""
import secrets
import hashlib
from datetime import datetime
from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Integer, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class ApiKey(BaseModel):
    """API key model for programmatic access"""
    
    __tablename__ = 'api_keys'
    
    # Relationships
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    
    # Key details
    name = Column(String(100), nullable=False)
    key_hash = Column(String(255), nullable=False)
    key_prefix = Column(String(10), nullable=False)
    
    # Permissions and limits
    permissions = Column(JSONB, default={})
    rate_limit_per_minute = Column(Integer, default=60, nullable=False)
    
    # Usage tracking
    last_used_at = Column(DateTime)
    expires_at = Column(DateTime)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship('User', back_populates='api_keys')
    
    def __init__(self, **kwargs):
        """Initialize API key with auto-generated key"""
        if 'key_hash' not in kwargs:
            key = self.generate_api_key()
            kwargs['key_hash'] = self.hash_key(key)
            kwargs['key_prefix'] = key[:8]
            # Store the plain key temporarily for return to user
            self._plain_key = key
        super().__init__(**kwargs)
    
    @staticmethod
    def generate_api_key():
        """Generate a new API key"""
        return f"pk_{''.join(secrets.choice('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(40))}"
    
    @staticmethod
    def hash_key(key):
        """Hash an API key"""
        return hashlib.sha256(key.encode()).hexdigest()
    
    def verify_key(self, key):
        """Verify an API key"""
        return self.key_hash == self.hash_key(key)
    
    def is_valid(self):
        """Check if API key is valid"""
        if not self.is_active:
            return False
        
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        
        return True
    
    def has_permission(self, permission):
        """Check if API key has specific permission"""
        return self.permissions.get(permission, False)
    
    def record_usage(self):
        """Record API key usage"""
        self.last_used_at = datetime.utcnow()
    
    def to_dict(self, exclude=None):
        """Convert to dictionary, excluding sensitive fields"""
        exclude = exclude or ['key_hash']
        data = super().to_dict(exclude=exclude)
        data['is_valid'] = self.is_valid()
        return data
    
    @classmethod
    def get_by_key(cls, key):
        """Get API key by key value"""
        key_hash = cls.hash_key(key)
        return cls.query.filter_by(key_hash=key_hash, is_active=True).first()
    
    def __repr__(self):
        return f'<ApiKey {self.name}>'
