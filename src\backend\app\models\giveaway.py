"""
Giveaway Models
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class Giveaway(BaseModel):
    """Giveaway model for promotional campaigns"""
    
    __tablename__ = 'giveaways'
    
    # Relationships
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    plan_id = Column(String(36), ForeignKey('plans.id'), nullable=False)
    
    # Giveaway details
    title = Column(String(200), nullable=False)
    description = Column(Text)
    total_licenses = Column(Integer, nullable=False)
    remaining_licenses = Column(Integer, nullable=False)
    
    # Timing
    start_at = Column(DateTime, nullable=False)
    end_at = Column(DateTime, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship('User', back_populates='giveaways')
    plan = relationship('Plan')
    entries = relationship('GiveawayEntry', back_populates='giveaway', lazy='dynamic', cascade='all, delete-orphan')
    
    def is_running(self):
        """Check if giveaway is currently running"""
        now = datetime.utcnow()
        return self.is_active and self.start_at <= now <= self.end_at
    
    def can_enter(self):
        """Check if giveaway can accept new entries"""
        return self.is_running() and self.remaining_licenses > 0
    
    def __repr__(self):
        return f'<Giveaway {self.title}>'


class GiveawayEntry(BaseModel):
    """Giveaway entry model"""
    
    __tablename__ = 'giveaway_entries'
    
    # Relationships
    giveaway_id = Column(String(36), ForeignKey('giveaways.id'), nullable=False)
    license_id = Column(String(36), ForeignKey('licenses.id'))

    # Entry details
    email = Column(String(255), nullable=False)
    ip_address = Column(String(45))  # IPv6 compatible
    is_winner = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    giveaway = relationship('Giveaway', back_populates='entries')
    license = relationship('License')
    
    def __repr__(self):
        return f'<GiveawayEntry {self.email}>'
