"""
Admin Routes
"""
from flask import Blueprint, request, jsonify, g
import structlog

from app import db
from app.models.user import User, UserRole
from app.models.plan import Plan
from app.models.license import License
from app.models.audit_log import AuditLog
from app.utils.auth import auth_required, role_required, get_client_ip, get_user_agent

logger = structlog.get_logger()

admin_bp = Blueprint('admin', __name__)


@admin_bp.route('/users', methods=['GET'])
@auth_required()
@role_required('admin', 'owner')
def list_users():
    """List all users (admin only)"""
    try:
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        
        users = User.query.offset(offset).limit(limit).all()
        total = User.query.count()
        
        return jsonify({
            'users': [user.to_dict() for user in users],
            'total': total,
            'limit': limit,
            'offset': offset
        }), 200
    
    except Exception as e:
        logger.error("List users failed", error=str(e))
        return jsonify({'error': 'Failed to list users'}), 500


@admin_bp.route('/plans', methods=['GET'])
@auth_required()
def list_plans():
    """List all plans"""
    try:
        plans = Plan.get_active_plans()
        return jsonify({
            'plans': [plan.to_dict() for plan in plans]
        }), 200
    
    except Exception as e:
        logger.error("List plans failed", error=str(e))
        return jsonify({'error': 'Failed to list plans'}), 500


@admin_bp.route('/plans', methods=['POST'])
@auth_required()
@role_required('admin', 'owner')
def create_plan():
    """Create a new plan (admin only)"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        plan = Plan.create(**data)
        
        # Log creation
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='plan_created',
            resource_type='plan',
            resource_id=plan.id,
            new_values=plan.to_dict(),
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        return jsonify({
            'message': 'Plan created successfully',
            'plan': plan.to_dict()
        }), 201
    
    except Exception as e:
        logger.error("Plan creation failed", error=str(e))
        return jsonify({'error': 'Failed to create plan'}), 500


@admin_bp.route('/stats', methods=['GET'])
@auth_required()
@role_required('admin', 'owner')
def get_admin_stats():
    """Get admin statistics"""
    try:
        stats = {
            'total_users': User.query.count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'total_licenses': License.query.count(),
            'active_licenses': License.query.filter_by(status='active').count(),
            'total_plans': Plan.query.count(),
            'active_plans': Plan.query.filter_by(is_active=True).count()
        }
        
        return jsonify(stats), 200
    
    except Exception as e:
        logger.error("Admin stats failed", error=str(e))
        return jsonify({'error': 'Failed to get stats'}), 500
