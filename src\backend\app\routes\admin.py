"""
Admin Routes
"""
from flask import Blueprint, request, jsonify, g, render_template, redirect, url_for, flash
import structlog

from app import db
from app.models.user import User, UserRole
from app.models.plan import Plan
from app.models.license import License
from app.models.audit_log import AuditLog
from flask_login import login_required, current_user

logger = structlog.get_logger()

admin_bp = Blueprint('admin', __name__)


@admin_bp.route('/dashboard')
@login_required
def admin_dashboard():
    """Admin dashboard"""
    if not current_user.is_admin:
        return redirect(url_for('main.dashboard'))

    try:
        from app.utils.maintenance import MaintenanceManager

        # Get system stats
        stats = {
            'total_users': User.query.count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'total_licenses': License.query.count(),
            'active_licenses': License.query.filter_by(status='active').count(),
            'validations_today': 0  # Placeholder
        }

        # Get health status
        health = MaintenanceManager.check_system_health()

        # Get recent activity
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
        recent_licenses = License.query.order_by(License.created_at.desc()).limit(5).all()

        return render_template('admin_dashboard.html',
                             stats=stats,
                             health=health,
                             recent_users=recent_users,
                             recent_licenses=recent_licenses)

    except Exception as e:
        flash('Failed to load admin dashboard', 'error')
        return redirect(url_for('main.dashboard'))


@admin_bp.route('/users', methods=['GET'])
@login_required
def list_users():
    """List all users (admin only)"""
    try:
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        
        users = User.query.offset(offset).limit(limit).all()
        total = User.query.count()
        
        return jsonify({
            'users': [user.to_dict() for user in users],
            'total': total,
            'limit': limit,
            'offset': offset
        }), 200
    
    except Exception as e:
        logger.error("List users failed", error=str(e))
        return jsonify({'error': 'Failed to list users'}), 500


@admin_bp.route('/plans', methods=['GET'])
@login_required
def list_plans():
    """List all plans"""
    try:
        plans = Plan.get_active_plans()
        return jsonify({
            'plans': [plan.to_dict() for plan in plans]
        }), 200

    except Exception as e:
        return jsonify({'error': 'Failed to list plans'}), 500


@admin_bp.route('/plans', methods=['POST'])
@login_required
def create_plan():
    """Create a new plan (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['name', 'type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        plan = Plan.create(**data)

        # Log creation
        AuditLog.log_action(
            user_id=current_user.id,
            action='plan_created',
            resource_type='plan',
            resource_id=plan.id,
            new_values=plan.to_dict(),
            ip_address=request.remote_addr
        )

        return jsonify({
            'message': 'Plan created successfully',
            'plan': plan.to_dict()
        }), 201

    except Exception as e:
        return jsonify({'error': 'Failed to create plan'}), 500


@admin_bp.route('/stats', methods=['GET'])
@login_required
def get_admin_stats():
    """Get admin statistics"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        stats = {
            'total_users': User.query.count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'total_licenses': License.query.count(),
            'active_licenses': License.query.filter_by(status='active').count(),
            'total_plans': Plan.query.count(),
            'active_plans': Plan.query.filter_by(is_active=True).count()
        }

        return jsonify(stats), 200

    except Exception as e:
        return jsonify({'error': 'Failed to get stats'}), 500


@admin_bp.route('/maintenance', methods=['POST'])
@login_required
def run_maintenance():
    """Run maintenance tasks"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        from app.utils.maintenance import run_maintenance_tasks
        results = run_maintenance_tasks()
        return jsonify(results), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@admin_bp.route('/backup', methods=['POST'])
@login_required
def create_backup():
    """Create system backup"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        from app.utils.maintenance import BackupManager
        backup_manager = BackupManager()
        backup_file = backup_manager.create_backup()
        return jsonify({'backup_file': backup_file}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@admin_bp.route('/report')
@login_required
def system_report():
    """Generate system report"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        from app.utils.maintenance import MaintenanceManager
        report = MaintenanceManager.generate_system_report()
        return jsonify(report), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
