"""
Analytics Routes
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, g
from sqlalchemy import func
import structlog

from app import db
from app.models.license import License, LicenseValidation, ValidationResult
from app.models.user import User
from app.utils.auth import auth_required, permission_required

logger = structlog.get_logger()

analytics_bp = Blueprint('analytics', __name__)


@analytics_bp.route('/dashboard', methods=['GET'])
@auth_required()
@permission_required('view_analytics')
def get_dashboard_analytics():
    """Get dashboard analytics"""
    try:
        period = request.args.get('period', '7d')
        
        # Calculate date range
        if period == '24h':
            start_date = datetime.utcnow() - timedelta(hours=24)
        elif period == '7d':
            start_date = datetime.utcnow() - timedelta(days=7)
        elif period == '30d':
            start_date = datetime.utcnow() - timedelta(days=30)
        elif period == '90d':
            start_date = datetime.utcnow() - timedelta(days=90)
        else:
            return jsonify({'error': 'Invalid period'}), 400
        
        # Get user's licenses only (unless admin)
        if g.current_user.is_admin:
            license_query = License.query
            validation_query = LicenseValidation.query
        else:
            license_query = g.current_user.licenses
            validation_query = LicenseValidation.query.join(License).filter(License.user_id == g.current_user.id)
        
        # Basic license stats
        total_licenses = license_query.count()
        active_licenses = license_query.filter_by(status='active').count()
        
        # Validation stats
        total_validations = validation_query.filter(LicenseValidation.created_at >= start_date).count()
        successful_validations = validation_query.filter(
            LicenseValidation.created_at >= start_date,
            LicenseValidation.result == ValidationResult.SUCCESS
        ).count()
        
        success_rate = (successful_validations / total_validations * 100) if total_validations > 0 else 0
        
        return jsonify({
            'total_licenses': total_licenses,
            'active_licenses': active_licenses,
            'total_validations': total_validations,
            'successful_validations': successful_validations,
            'validation_success_rate': round(success_rate, 2),
            'period': period
        }), 200
    
    except Exception as e:
        logger.error("Dashboard analytics failed", error=str(e))
        return jsonify({'error': 'Failed to get analytics'}), 500


@analytics_bp.route('/validations', methods=['GET'])
@auth_required()
@permission_required('view_analytics')
def get_validation_analytics():
    """Get validation analytics"""
    try:
        period = request.args.get('period', '7d')
        license_id = request.args.get('license_id')
        
        # Calculate date range
        if period == '24h':
            start_date = datetime.utcnow() - timedelta(hours=24)
        elif period == '7d':
            start_date = datetime.utcnow() - timedelta(days=7)
        elif period == '30d':
            start_date = datetime.utcnow() - timedelta(days=30)
        elif period == '90d':
            start_date = datetime.utcnow() - timedelta(days=90)
        else:
            return jsonify({'error': 'Invalid period'}), 400
        
        # Build query
        if g.current_user.is_admin:
            query = LicenseValidation.query
        else:
            query = LicenseValidation.query.join(License).filter(License.user_id == g.current_user.id)
        
        query = query.filter(LicenseValidation.created_at >= start_date)
        
        if license_id:
            query = query.filter(LicenseValidation.license_id == license_id)
        
        # Get validation counts by result
        result_counts = db.session.query(
            LicenseValidation.result,
            func.count(LicenseValidation.id)
        ).filter(
            LicenseValidation.created_at >= start_date
        ).group_by(LicenseValidation.result).all()
        
        error_breakdown = {result.value: count for result, count in result_counts}
        
        # Get geographic distribution
        geo_counts = db.session.query(
            LicenseValidation.country,
            func.count(LicenseValidation.id)
        ).filter(
            LicenseValidation.created_at >= start_date,
            LicenseValidation.country.isnot(None)
        ).group_by(LicenseValidation.country).limit(10).all()
        
        geographic_distribution = {country: count for country, count in geo_counts}
        
        # Get hourly distribution
        hourly_counts = db.session.query(
            func.extract('hour', LicenseValidation.created_at).label('hour'),
            func.count(LicenseValidation.id)
        ).filter(
            LicenseValidation.created_at >= start_date
        ).group_by('hour').all()
        
        hourly_distribution = [{'hour': int(hour), 'count': count} for hour, count in hourly_counts]
        
        total_validations = sum(count for _, count in result_counts)
        successful_validations = error_breakdown.get(ValidationResult.SUCCESS.value, 0)
        success_rate = (successful_validations / total_validations * 100) if total_validations > 0 else 0
        
        return jsonify({
            'total_validations': total_validations,
            'success_rate': round(success_rate, 2),
            'error_breakdown': error_breakdown,
            'geographic_distribution': geographic_distribution,
            'hourly_distribution': hourly_distribution,
            'period': period
        }), 200
    
    except Exception as e:
        logger.error("Validation analytics failed", error=str(e))
        return jsonify({'error': 'Failed to get validation analytics'}), 500
