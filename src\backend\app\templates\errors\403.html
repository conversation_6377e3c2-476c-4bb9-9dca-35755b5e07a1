{% extends "base.html" %}

{% block title %}Access Denied - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="w-24 h-24 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-8">
            <i data-lucide="shield-x" class="w-12 h-12 text-white"></i>
        </div>

        <!-- Error Message -->
        <h1 class="text-6xl font-bold text-slate-900 dark:text-white mb-4">403</h1>
        <h2 class="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
            Access Denied
        </h2>
        <p class="text-slate-600 dark:text-slate-400 mb-8">
            You don't have permission to access this resource.
        </p>

        <!-- Action Buttons -->
        <div class="space-y-4">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for('main.dashboard') }}" class="btn-primary w-full">
                    <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                    Go to Dashboard
                </a>
            {% else %}
                <a href="{{ url_for('auth.login') }}" class="btn-primary w-full">
                    <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                    Sign In
                </a>
            {% endif %}
            <button onclick="history.back()" class="btn-secondary w-full">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                Go Back
            </button>
        </div>

        <!-- Help Info -->
        <div class="mt-8 pt-8 border-t border-white/10">
            <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                Need access to this resource?
            </p>
            <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500 text-sm">
                Contact Support
            </a>
        </div>
    </div>
</div>
{% endblock %}
