{% extends "base.html" %}

{% block title %}Plans & Pricing - <PERSON><PERSON><PERSON>th{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            Choose Your Plan
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Scale your licensing needs with our flexible pricing plans. Start free and upgrade as you grow.
        </p>
    </div>

    <!-- Pricing Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        {% for plan in plans %}
            <div class="card {% if plan.type.value == 'pro' %}ring-2 ring-primary-500 relative{% endif %}">
                {% if plan.type.value == 'pro' %}
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-gradient-to-r from-primary-500 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                            Most Popular
                        </span>
                    </div>
                {% endif %}
                
                <div class="text-center">
                    <!-- Plan Icon -->
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r 
                        {% if plan.type.value == 'free_trial' %}from-gray-400 to-gray-600
                        {% elif plan.type.value == 'hobby' %}from-green-400 to-green-600
                        {% elif plan.type.value == 'pro' %}from-primary-500 to-blue-600
                        {% elif plan.type.value == 'enterprise' %}from-purple-500 to-indigo-600
                        {% endif %} rounded-2xl flex items-center justify-center">
                        {% if plan.type.value == 'free_trial' %}
                            <i data-lucide="gift" class="w-8 h-8 text-white"></i>
                        {% elif plan.type.value == 'hobby' %}
                            <i data-lucide="heart" class="w-8 h-8 text-white"></i>
                        {% elif plan.type.value == 'pro' %}
                            <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        {% elif plan.type.value == 'enterprise' %}
                            <i data-lucide="building" class="w-8 h-8 text-white"></i>
                        {% endif %}
                    </div>

                    <!-- Plan Name -->
                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                        {{ plan.name }}
                    </h3>

                    <!-- Plan Description -->
                    <p class="text-slate-600 dark:text-slate-400 mb-6">
                        {{ plan.description }}
                    </p>

                    <!-- Plan Price -->
                    <div class="mb-6">
                        {% if plan.price_monthly == 0 %}
                            <div class="text-4xl font-bold text-slate-900 dark:text-white">Free</div>
                            <div class="text-slate-600 dark:text-slate-400">30-day trial</div>
                        {% else %}
                            <div class="text-4xl font-bold text-slate-900 dark:text-white">
                                ${{ "%.0f"|format(plan.price_monthly) }}
                                <span class="text-lg font-normal text-slate-600 dark:text-slate-400">/month</span>
                            </div>
                            {% if plan.price_yearly %}
                                <div class="text-slate-600 dark:text-slate-400">
                                    or ${{ "%.0f"|format(plan.price_yearly) }}/year
                                    <span class="text-green-600 dark:text-green-400 text-sm font-medium">
                                        (Save {{ "%.0f"|format((plan.price_monthly * 12 - plan.price_yearly) / (plan.price_monthly * 12) * 100) }}%)
                                    </span>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>

                    <!-- CTA Button -->
                    {% if current_user.is_authenticated %}
                        <button class="{% if plan.type.value == 'pro' %}btn-primary{% else %}btn-secondary{% endif %} w-full mb-6"
                                onclick="alert('Billing integration coming soon!')">
                            {% if plan.price_monthly == 0 %}
                                Start Free Trial
                            {% else %}
                                Upgrade to {{ plan.name }}
                            {% endif %}
                        </button>
                    {% else %}
                        <a href="{{ url_for('auth.register') }}" 
                           class="{% if plan.type.value == 'pro' %}btn-primary{% else %}btn-secondary{% endif %} w-full mb-6">
                            Get Started
                        </a>
                    {% endif %}

                    <!-- Plan Features -->
                    <div class="text-left space-y-3">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                            <span class="text-sm text-slate-600 dark:text-slate-400">
                                {% if plan.max_licenses == -1 %}
                                    Unlimited licenses
                                {% else %}
                                    {{ plan.max_licenses }} license{{ 's' if plan.max_licenses != 1 else '' }}
                                {% endif %}
                            </span>
                        </div>

                        <div class="flex items-center space-x-3">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                            <span class="text-sm text-slate-600 dark:text-slate-400">
                                {% if plan.max_devices_per_license == -1 %}
                                    Unlimited devices per license
                                {% else %}
                                    {{ plan.max_devices_per_license }} device{{ 's' if plan.max_devices_per_license != 1 else '' }} per license
                                {% endif %}
                            </span>
                        </div>

                        <div class="flex items-center space-x-3">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                            <span class="text-sm text-slate-600 dark:text-slate-400">
                                {% if plan.max_api_calls_per_month == -1 %}
                                    Unlimited API calls
                                {% else %}
                                    {{ "{:,}".format(plan.max_api_calls_per_month) }} API calls/month
                                {% endif %}
                            </span>
                        </div>

                        {% if plan.type.value != 'free_trial' %}
                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">Advanced analytics</span>
                            </div>
                        {% endif %}

                        {% if plan.type.value in ['pro', 'enterprise'] %}
                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">Webhook notifications</span>
                            </div>

                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">Priority support</span>
                            </div>
                        {% endif %}

                        {% if plan.type.value == 'enterprise' %}
                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">Custom integrations</span>
                            </div>

                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">Dedicated support</span>
                            </div>

                            <div class="flex items-center space-x-3">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 flex-shrink-0"></i>
                                <span class="text-sm text-slate-600 dark:text-slate-400">SLA guarantee</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Feature Comparison -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Feature Comparison</h3>
            <p class="card-description">Compare all features across our plans</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="table-glass w-full">
                <thead>
                    <tr>
                        <th class="text-left">Feature</th>
                        {% for plan in plans %}
                            <th class="text-center">{{ plan.name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="font-medium">License Validation API</td>
                        {% for plan in plans %}
                            <td class="text-center">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 mx-auto"></i>
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">Hardware ID Locking</td>
                        {% for plan in plans %}
                            <td class="text-center">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 mx-auto"></i>
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">Basic Analytics</td>
                        {% for plan in plans %}
                            <td class="text-center">
                                <i data-lucide="check" class="w-4 h-4 text-green-500 mx-auto"></i>
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">Advanced Analytics</td>
                        {% for plan in plans %}
                            <td class="text-center">
                                {% if plan.type.value != 'free_trial' %}
                                    <i data-lucide="check" class="w-4 h-4 text-green-500 mx-auto"></i>
                                {% else %}
                                    <i data-lucide="x" class="w-4 h-4 text-gray-400 mx-auto"></i>
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">Webhook Notifications</td>
                        {% for plan in plans %}
                            <td class="text-center">
                                {% if plan.type.value in ['pro', 'enterprise'] %}
                                    <i data-lucide="check" class="w-4 h-4 text-green-500 mx-auto"></i>
                                {% else %}
                                    <i data-lucide="x" class="w-4 h-4 text-gray-400 mx-auto"></i>
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">API Rate Limits</td>
                        {% for plan in plans %}
                            <td class="text-center text-sm">
                                {% if plan.max_api_calls_per_month == -1 %}
                                    Unlimited
                                {% else %}
                                    {{ "{:,}".format(plan.max_api_calls_per_month) }}/month
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                    <tr>
                        <td class="font-medium">Support Level</td>
                        {% for plan in plans %}
                            <td class="text-center text-sm">
                                {% if plan.type.value == 'free_trial' %}
                                    Community
                                {% elif plan.type.value == 'hobby' %}
                                    Email
                                {% elif plan.type.value == 'pro' %}
                                    Priority
                                {% elif plan.type.value == 'enterprise' %}
                                    Dedicated
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-16 card">
        <div class="card-header">
            <h3 class="card-title">Frequently Asked Questions</h3>
            <p class="card-description">Common questions about our pricing and features</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">Can I change plans anytime?</h4>
                <p class="text-sm text-slate-600 dark:text-slate-400">
                    Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">What happens if I exceed my limits?</h4>
                <p class="text-sm text-slate-600 dark:text-slate-400">
                    We'll notify you when you approach your limits. You can upgrade your plan to continue service.
                </p>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">Do you offer refunds?</h4>
                <p class="text-sm text-slate-600 dark:text-slate-400">
                    Yes, we offer a 30-day money-back guarantee for all paid plans.
                </p>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">Is there a setup fee?</h4>
                <p class="text-sm text-slate-600 dark:text-slate-400">
                    No setup fees. You only pay the monthly or yearly subscription price.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
