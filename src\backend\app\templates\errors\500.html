{% extends "base.html" %}

{% block title %}Server Erro<PERSON> <PERSON> <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="w-24 h-24 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-8">
            <i data-lucide="alert-triangle" class="w-12 h-12 text-white"></i>
        </div>

        <!-- Error Message -->
        <h1 class="text-6xl font-bold text-slate-900 dark:text-white mb-4">500</h1>
        <h2 class="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
            Server Error
        </h2>
        <p class="text-slate-600 dark:text-slate-400 mb-8">
            Something went wrong on our end. We're working to fix it.
        </p>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="window.location.reload()" class="btn-primary w-full">
                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                Try Again
            </button>
            <a href="{{ url_for('main.index') }}" class="btn-secondary w-full">
                <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                Go Home
            </a>
        </div>

        <!-- Support Info -->
        <div class="mt-8 pt-8 border-t border-white/10">
            <p class="text-sm text-slate-600 dark:text-slate-400 mb-4">
                If this problem persists, please contact support
            </p>
            <div class="space-y-2">
                <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500 text-sm">
                    <EMAIL>
                </a>
                <br>
                <a href="{{ url_for('status.status_page') }}" class="text-primary-600 hover:text-primary-500 text-sm">
                    Check System Status
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
