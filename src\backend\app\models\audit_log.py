"""
Audit Log Model
"""
from sqlalchemy import Column, String, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class AuditLog(BaseModel):
    """Audit log model for tracking user actions"""
    
    __tablename__ = 'audit_logs'
    
    # Relationships
    user_id = Column(String(36), ForeignKey('users.id'))

    # Action details
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String(36))

    # Change tracking
    old_values = Column(JSON)
    new_values = Column(JSON)

    # Request details
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    
    # Relationships
    user = relationship('User', back_populates='audit_logs')
    
    @classmethod
    def log_action(cls, user_id, action, resource_type, resource_id=None, 
                   old_values=None, new_values=None, ip_address=None, user_agent=None):
        """Log an audit action"""
        log = cls(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent
        )
        return log.save()
    
    def __repr__(self):
        return f'<AuditLog {self.action} on {self.resource_type}>'
