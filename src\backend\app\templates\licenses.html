{% extends "base.html" %}

{% block title %}Licenses - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Licenses</h1>
            <p class="text-slate-600 dark:text-slate-400 mt-2">
                Manage your license keys and track their usage
            </p>
        </div>
        <div>
            <a href="{{ url_for('licenses.create_license') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create License
            </a>
        </div>
    </div>

    <!-- Licenses Table -->
    {% if licenses %}
        <div class="card">
            <div class="overflow-x-auto">
                <table class="table-glass w-full">
                    <thead>
                        <tr>
                            <th>License Key</th>
                            <th>Plan</th>
                            <th>Status</th>
                            <th>Devices</th>
                            <th>Created</th>
                            <th>Expires</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for license in licenses %}
                            <tr>
                                <td>
                                    <div class="font-mono text-sm">
                                        <a href="{{ url_for('licenses.view_license', license_id=license.id) }}" class="text-primary-600 hover:text-primary-500">
                                            {{ license.license_key[:8] }}...{{ license.license_key[-4:] }}
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <span class="font-medium">{{ license.plan.name }}</span>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ license.status.value }}">
                                        {{ license.status.value.title() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {{ license.current_devices }}/{{ license.max_devices }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {{ license.created_at.strftime('%b %d, %Y') }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {% if license.expires_at %}
                                            {{ license.expires_at.strftime('%b %d, %Y') }}
                                        {% else %}
                                            Never
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <div class="flex space-x-2">
                                        <button class="btn-ghost text-xs" onclick="copyToClipboard('{{ license.license_key }}')">
                                            <i data-lucide="copy" class="w-3 h-3"></i>
                                        </button>
                                        <button class="btn-ghost text-xs" onclick="alert('Edit functionality coming soon!')">
                                            <i data-lucide="edit" class="w-3 h-3"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination and pagination.pages > 1 %}
                <div class="flex justify-between items-center mt-6 pt-6 border-t border-white/10">
                    <div class="text-sm text-slate-600 dark:text-slate-400">
                        Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} to 
                        {{ pagination.per_page * pagination.page if pagination.page < pagination.pages else pagination.total }} 
                        of {{ pagination.total }} licenses
                    </div>
                    <div class="flex space-x-2">
                        {% if pagination.has_prev %}
                            <a href="{{ url_for('main.licenses', page=pagination.prev_num) }}" class="btn-ghost">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            </a>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                    <a href="{{ url_for('main.licenses', page=page_num) }}" class="btn-ghost">
                                        {{ page_num }}
                                    </a>
                                {% else %}
                                    <span class="btn-primary">{{ page_num }}</span>
                                {% endif %}
                            {% else %}
                                <span class="text-slate-400">...</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                            <a href="{{ url_for('main.licenses', page=pagination.next_num) }}" class="btn-ghost">
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="card text-center py-12">
            <div class="w-24 h-24 bg-gradient-to-r from-primary-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="key" class="w-12 h-12 text-primary-600"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                No licenses yet
            </h3>
            <p class="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                Create your first license to start protecting your software and tracking usage.
            </p>
            <a href="{{ url_for('licenses.create_license') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create Your First License
            </a>
        </div>
    {% endif %}
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = 'License key copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Failed to copy license key');
    });
}
</script>
{% endblock %}
