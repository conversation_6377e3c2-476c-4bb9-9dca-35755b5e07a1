{% extends "base.html" %}

{% block title %}Admin Dashboard - <PERSON><PERSON><PERSON>th{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Admin Dashboard</h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            System overview and management tools
        </p>
    </div>

    <!-- System Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ stats.total_users or 0 }}</p>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Total Users</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="key" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ stats.total_licenses or 0 }}</p>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Total Licenses</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="activity" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ stats.validations_today or 0 }}</p>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Validations Today</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                    <i data-lucide="server" class="w-6 h-6 text-white"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-slate-900 dark:text-white">
                        {% if health.status == 'healthy' %}
                            <span class="text-green-600">Healthy</span>
                        {% elif health.status == 'warning' %}
                            <span class="text-yellow-600">Warning</span>
                        {% else %}
                            <span class="text-red-600">Issues</span>
                        {% endif %}
                    </p>
                    <p class="text-sm text-slate-600 dark:text-slate-400">System Status</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Quick Actions</h3>
                <p class="card-description">Common administrative tasks</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <button class="btn-secondary" onclick="runMaintenance()">
                    <i data-lucide="wrench" class="w-4 h-4 mr-2"></i>
                    Run Maintenance
                </button>
                <button class="btn-secondary" onclick="createBackup()">
                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                    Create Backup
                </button>
                <button class="btn-secondary" onclick="viewLogs()">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    View Logs
                </button>
                <button class="btn-secondary" onclick="systemReport()">
                    <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                    System Report
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">System Health</h3>
                <p class="card-description">Current system status</p>
            </div>
            
            <div class="space-y-3">
                {% if health.checks %}
                    {% for check_name, check_result in health.checks.items() %}
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-slate-600 dark:text-slate-400">{{ check_name.replace('_', ' ').title() }}</span>
                            <span class="text-sm {% if 'OK' in check_result %}text-green-600{% elif 'WARNING' in check_result %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {{ check_result }}
                            </span>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-sm text-slate-600 dark:text-slate-400">Health check data unavailable</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Users</h3>
                <p class="card-description">Latest user registrations</p>
            </div>
            
            {% if recent_users %}
                <div class="space-y-3">
                    {% for user in recent_users %}
                        <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                            <div>
                                <p class="font-medium text-slate-900 dark:text-white">{{ user.username }}</p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">{{ user.email }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ user.created_at.strftime('%b %d') if user.created_at else 'Unknown' }}
                                </p>
                                <span class="status-badge {% if user.is_active %}status-active{% else %}status-inactive{% endif %}">
                                    {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-center text-slate-600 dark:text-slate-400 py-8">No recent users</p>
            {% endif %}
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Licenses</h3>
                <p class="card-description">Latest license creations</p>
            </div>
            
            {% if recent_licenses %}
                <div class="space-y-3">
                    {% for license in recent_licenses %}
                        <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                            <div>
                                <p class="font-mono text-sm text-slate-900 dark:text-white">
                                    {{ license.license_key[:8] }}...{{ license.license_key[-4:] }}
                                </p>
                                <p class="text-sm text-slate-600 dark:text-slate-400">{{ license.plan.name if license.plan else 'Unknown Plan' }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ license.created_at.strftime('%b %d') if license.created_at else 'Unknown' }}
                                </p>
                                <span class="status-badge status-{{ license.status.value if license.status else 'unknown' }}">
                                    {{ license.status.value.title() if license.status else 'Unknown' }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-center text-slate-600 dark:text-slate-400 py-8">No recent licenses</p>
            {% endif %}
        </div>
    </div>
</div>

<script>
function runMaintenance() {
    if (confirm('This will run system maintenance tasks. Continue?')) {
        fetch('/admin/maintenance', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                alert('Maintenance completed: ' + JSON.stringify(data, null, 2));
                location.reload();
            })
            .catch(error => {
                alert('Maintenance failed: ' + error);
            });
    }
}

function createBackup() {
    if (confirm('Create a system backup?')) {
        fetch('/admin/backup', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                alert('Backup created: ' + data.backup_file);
            })
            .catch(error => {
                alert('Backup failed: ' + error);
            });
    }
}

function viewLogs() {
    window.open('/admin/logs', '_blank');
}

function systemReport() {
    window.open('/admin/report', '_blank');
}

// Auto-refresh every 30 seconds
setInterval(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
