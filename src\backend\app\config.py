"""
Flask Configuration Classes
"""
import os
from datetime import timedelta
from environs import Env

env = Env()
env.read_env()


class BaseConfig:
    """Base configuration class"""
    
    # Flask settings
    SECRET_KEY = env.str('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database settings
    SQLALCHEMY_DATABASE_URI = env.str(
        'DATABASE_URL',
        'postgresql://postgres:password@localhost:5432/pepe_auth'
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_recycle': 3600,
        'pool_pre_ping': True
    }
    
    # Redis settings
    REDIS_URL = env.str('REDIS_URL', 'redis://localhost:6379/0')
    
    # JWT settings
    JWT_SECRET_KEY = env.str('JWT_SECRET_KEY', SECRET_KEY)
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    
    # Security settings
    BCRYPT_LOG_ROUNDS = 12
    PASSWORD_MIN_LENGTH = 8
    MAX_LOGIN_ATTEMPTS = 5
    ACCOUNT_LOCKOUT_DURATION = timedelta(minutes=30)
    
    # Rate limiting
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_STRATEGY = 'fixed-window'
    
    # CORS settings
    CORS_ORIGINS = env.list('CORS_ORIGINS', ['http://localhost:3000'])
    
    # Mail settings
    MAIL_SERVER = env.str('MAIL_SERVER', 'localhost')
    MAIL_PORT = env.int('MAIL_PORT', 587)
    MAIL_USE_TLS = env.bool('MAIL_USE_TLS', True)
    MAIL_USERNAME = env.str('MAIL_USERNAME', '')
    MAIL_PASSWORD = env.str('MAIL_PASSWORD', '')
    MAIL_DEFAULT_SENDER = env.str('MAIL_DEFAULT_SENDER', '<EMAIL>')
    
    # License settings
    LICENSE_SIGNATURE_KEY = env.str('LICENSE_SIGNATURE_KEY', 'change-in-production')
    LICENSE_VALIDATION_WINDOW = timedelta(minutes=2)
    OFFLINE_LICENSE_MAX_DAYS = 30
    
    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = env.str('UPLOAD_FOLDER', 'uploads')
    
    # Pagination
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # Logging
    LOG_LEVEL = env.str('LOG_LEVEL', 'INFO')
    
    # Feature flags
    ENABLE_REGISTRATION = env.bool('ENABLE_REGISTRATION', True)
    ENABLE_2FA = env.bool('ENABLE_2FA', True)
    ENABLE_EMAIL_VERIFICATION = env.bool('ENABLE_EMAIL_VERIFICATION', True)
    
    # External services
    SENTRY_DSN = env.str('SENTRY_DSN', '')
    STRIPE_SECRET_KEY = env.str('STRIPE_SECRET_KEY', '')
    STRIPE_WEBHOOK_SECRET = env.str('STRIPE_WEBHOOK_SECRET', '')


class DevelopmentConfig(BaseConfig):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # More verbose logging in development
    LOG_LEVEL = 'DEBUG'
    
    # Relaxed security for development
    BCRYPT_LOG_ROUNDS = 4
    
    # Development database
    SQLALCHEMY_DATABASE_URI = env.str(
        'DEV_DATABASE_URL',
        'postgresql://postgres:password@localhost:5432/pepe_auth_dev'
    )


class TestingConfig(BaseConfig):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    
    # Use in-memory SQLite for tests
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False
    
    # Fast password hashing for tests
    BCRYPT_LOG_ROUNDS = 4
    
    # Short token expiry for testing
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(seconds=30)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(minutes=5)
    
    # Disable rate limiting in tests
    RATELIMIT_ENABLED = False
    
    # Use test Redis database
    REDIS_URL = env.str('TEST_REDIS_URL', 'redis://localhost:6379/1')


class StagingConfig(BaseConfig):
    """Staging configuration"""
    DEBUG = False
    TESTING = False
    
    # Staging database
    SQLALCHEMY_DATABASE_URI = env.str('STAGING_DATABASE_URL')
    
    # Enable Sentry for staging
    SENTRY_DSN = env.str('STAGING_SENTRY_DSN', '')


class ProductionConfig(BaseConfig):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production database
    SQLALCHEMY_DATABASE_URI = env.str('DATABASE_URL')
    
    # Enhanced security for production
    BCRYPT_LOG_ROUNDS = 14
    
    # Strict CORS in production
    CORS_ORIGINS = env.list('CORS_ORIGINS')
    
    # Enable Sentry for production
    SENTRY_DSN = env.str('SENTRY_DSN')
    
    # SSL settings
    PREFERRED_URL_SCHEME = 'https'
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'staging': StagingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
