"""
Maintenance and Backup Utilities
"""
import os
import shutil
import subprocess
import json
from datetime import datetime, timedelta
from pathlib import Path

from app import db
from app.models.license import License, LicenseValidation
from app.models.user import User
from app.models.audit_log import AuditLog


class BackupManager:
    """Database backup and restore utilities"""
    
    def __init__(self, backup_dir="backups"):
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_backup(self, include_data=True):
        """Create a database backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"backup_{timestamp}.sql"
        
        try:
            # For SQLite, we can simply copy the file
            db_url = os.environ.get('DATABASE_URL', 'sqlite:///pepe_auth.db')
            
            if db_url.startswith('sqlite:'):
                db_file = db_url.replace('sqlite:///', '')
                if os.path.exists(db_file):
                    shutil.copy2(db_file, str(backup_file).replace('.sql', '.db'))
                    return str(backup_file).replace('.sql', '.db')
            
            # For PostgreSQL/MySQL, use pg_dump/mysqldump
            # This is a placeholder - would need proper implementation
            return str(backup_file)
            
        except Exception as e:
            raise Exception(f"Backup failed: {str(e)}")
    
    def restore_backup(self, backup_file):
        """Restore from a backup file"""
        if not os.path.exists(backup_file):
            raise FileNotFoundError(f"Backup file not found: {backup_file}")
        
        try:
            # Implementation would depend on database type
            # For SQLite, replace the current file
            # For PostgreSQL/MySQL, use restore commands
            pass
        except Exception as e:
            raise Exception(f"Restore failed: {str(e)}")
    
    def list_backups(self):
        """List available backups"""
        backups = []
        for backup_file in self.backup_dir.glob("backup_*"):
            stat = backup_file.stat()
            backups.append({
                'filename': backup_file.name,
                'path': str(backup_file),
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime)
            })
        
        return sorted(backups, key=lambda x: x['created'], reverse=True)
    
    def cleanup_old_backups(self, keep_days=30):
        """Remove backups older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        removed_count = 0
        
        for backup_file in self.backup_dir.glob("backup_*"):
            if datetime.fromtimestamp(backup_file.stat().st_ctime) < cutoff_date:
                backup_file.unlink()
                removed_count += 1
        
        return removed_count


class MaintenanceManager:
    """System maintenance utilities"""
    
    @staticmethod
    def cleanup_expired_sessions():
        """Clean up expired user sessions"""
        # This would depend on session storage implementation
        pass
    
    @staticmethod
    def cleanup_old_logs(days=90):
        """Clean up old audit logs"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        deleted_count = AuditLog.query.filter(
            AuditLog.created_at < cutoff_date
        ).delete()
        
        db.session.commit()
        return deleted_count
    
    @staticmethod
    def cleanup_old_validations(days=365):
        """Clean up old license validations"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        deleted_count = LicenseValidation.query.filter(
            LicenseValidation.created_at < cutoff_date
        ).delete()
        
        db.session.commit()
        return deleted_count
    
    @staticmethod
    def optimize_database():
        """Optimize database performance"""
        try:
            # For SQLite
            db.session.execute("VACUUM")
            db.session.execute("ANALYZE")
            db.session.commit()
            return "Database optimized successfully"
        except Exception as e:
            return f"Database optimization failed: {str(e)}"
    
    @staticmethod
    def check_system_health():
        """Perform system health checks"""
        health_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'status': 'healthy',
            'checks': {}
        }
        
        # Database connectivity
        try:
            db.session.execute("SELECT 1")
            health_report['checks']['database'] = 'OK'
        except Exception as e:
            health_report['checks']['database'] = f'ERROR: {str(e)}'
            health_report['status'] = 'unhealthy'
        
        # Disk space
        try:
            disk_usage = shutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            if free_gb < 1:  # Less than 1GB free
                health_report['checks']['disk_space'] = f'WARNING: Only {free_gb:.1f}GB free'
                health_report['status'] = 'warning'
            else:
                health_report['checks']['disk_space'] = f'OK: {free_gb:.1f}GB free'
        except Exception as e:
            health_report['checks']['disk_space'] = f'ERROR: {str(e)}'
        
        # License validation performance
        try:
            recent_validations = LicenseValidation.query.filter(
                LicenseValidation.created_at >= datetime.utcnow() - timedelta(hours=1)
            ).count()
            
            if recent_validations > 1000:  # High load
                health_report['checks']['api_load'] = f'HIGH: {recent_validations} validations/hour'
            else:
                health_report['checks']['api_load'] = f'OK: {recent_validations} validations/hour'
        except Exception as e:
            health_report['checks']['api_load'] = f'ERROR: {str(e)}'
        
        return health_report
    
    @staticmethod
    def generate_system_report():
        """Generate comprehensive system report"""
        report = {
            'generated_at': datetime.utcnow().isoformat(),
            'system_info': {
                'total_users': User.query.count(),
                'active_users': User.query.filter_by(is_active=True).count(),
                'total_licenses': License.query.count(),
                'active_licenses': License.query.filter_by(status='active').count(),
                'expired_licenses': License.query.filter(
                    License.expires_at < datetime.utcnow()
                ).count()
            },
            'usage_stats': {
                'validations_today': LicenseValidation.query.filter(
                    LicenseValidation.created_at >= datetime.utcnow().date()
                ).count(),
                'validations_this_week': LicenseValidation.query.filter(
                    LicenseValidation.created_at >= datetime.utcnow() - timedelta(days=7)
                ).count(),
                'validations_this_month': LicenseValidation.query.filter(
                    LicenseValidation.created_at >= datetime.utcnow() - timedelta(days=30)
                ).count()
            },
            'health_check': MaintenanceManager.check_system_health()
        }
        
        return report


class DataExporter:
    """Data export utilities"""
    
    @staticmethod
    def export_user_data(user_id, format='json'):
        """Export all data for a specific user"""
        user = User.get_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        data = {
            'user': user.to_dict(),
            'licenses': [license.to_dict() for license in user.licenses],
            'audit_logs': [
                log.to_dict() for log in AuditLog.query.filter_by(user_id=user_id).all()
            ]
        }
        
        if format == 'json':
            return json.dumps(data, indent=2, default=str)
        else:
            raise ValueError("Unsupported format")
    
    @staticmethod
    def export_system_data(format='json'):
        """Export system-wide data"""
        data = {
            'users': [user.to_dict() for user in User.query.all()],
            'licenses': [license.to_dict() for license in License.query.all()],
            'export_timestamp': datetime.utcnow().isoformat()
        }
        
        if format == 'json':
            return json.dumps(data, indent=2, default=str)
        else:
            raise ValueError("Unsupported format")


def run_maintenance_tasks():
    """Run all maintenance tasks"""
    results = {}
    
    # Cleanup old logs
    results['old_logs_cleaned'] = MaintenanceManager.cleanup_old_logs()
    
    # Cleanup old validations
    results['old_validations_cleaned'] = MaintenanceManager.cleanup_old_validations()
    
    # Optimize database
    results['database_optimization'] = MaintenanceManager.optimize_database()
    
    # Create backup
    backup_manager = BackupManager()
    try:
        backup_file = backup_manager.create_backup()
        results['backup_created'] = backup_file
    except Exception as e:
        results['backup_error'] = str(e)
    
    # Cleanup old backups
    results['old_backups_cleaned'] = backup_manager.cleanup_old_backups()
    
    return results
