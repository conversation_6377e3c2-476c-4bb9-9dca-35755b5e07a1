"""
Documentation Routes
"""
from flask import Blueprint, render_template

docs_bp = Blueprint('docs', __name__)


@docs_bp.route('/')
def index():
    """Documentation home page"""
    return render_template('docs/index.html')


@docs_bp.route('/getting-started')
def getting_started():
    """Getting started guide"""
    return render_template('docs/getting_started.html')


@docs_bp.route('/api-reference')
def api_reference():
    """API reference documentation"""
    return render_template('docs/api_reference.html')


@docs_bp.route('/webhooks')
def webhooks():
    """Webhook documentation"""
    return render_template('docs/webhooks.html')


@docs_bp.route('/sdks')
def sdks():
    """SDK documentation"""
    return render_template('docs/sdks.html')


@docs_bp.route('/examples')
def examples():
    """Code examples"""
    return render_template('docs/examples.html')
