{% extends "base.html" %}

{% block title %}System Status - Pepe Auth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-slate-900 dark:text-white mb-4">
            System Status
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400">
            Current operational status of Pepe Auth services
        </p>
    </div>

    <!-- Overall Status -->
    <div class="card mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-4 h-4 rounded-full 
                    {% if status.overall == 'operational' %}bg-green-500
                    {% elif status.overall == 'degraded' %}bg-yellow-500
                    {% else %}bg-red-500{% endif %}">
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-slate-900 dark:text-white">
                        {% if status.overall == 'operational' %}All Systems Operational
                        {% elif status.overall == 'degraded' %}Degraded Performance
                        {% else %}Service Disruption{% endif %}
                    </h2>
                    <p class="text-slate-600 dark:text-slate-400">
                        Last updated: {{ status.timestamp[:19] if status.timestamp else 'Unknown' }}
                    </p>
                </div>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold text-slate-900 dark:text-white">{{ status.uptime or '99.9%' }}</div>
                <div class="text-sm text-slate-600 dark:text-slate-400">Uptime (30 days)</div>
            </div>
        </div>
    </div>

    <!-- Service Status -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="card-title">Service Status</h3>
            <p class="card-description">Current status of individual services</p>
        </div>
        
        <div class="space-y-4">
            {% if status.services %}
                {% for service_name, service_data in status.services.items() %}
                    <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                        <div class="flex items-center space-x-4">
                            <div class="w-3 h-3 rounded-full 
                                {% if service_data.status == 'operational' %}bg-green-500
                                {% elif service_data.status == 'degraded' %}bg-yellow-500
                                {% else %}bg-red-500{% endif %}">
                            </div>
                            <div>
                                <h4 class="font-medium text-slate-900 dark:text-white">
                                    {{ service_name.replace('_', ' ').title() }}
                                </h4>
                                {% if service_data.error %}
                                    <p class="text-sm text-red-600 dark:text-red-400">{{ service_data.error }}</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium 
                                {% if service_data.status == 'operational' %}text-green-600 dark:text-green-400
                                {% elif service_data.status == 'degraded' %}text-yellow-600 dark:text-yellow-400
                                {% else %}text-red-600 dark:text-red-400{% endif %}">
                                {{ service_data.status.title() }}
                            </div>
                            {% if service_data.response_time_ms %}
                                <div class="text-xs text-slate-500">{{ service_data.response_time_ms }}ms</div>
                            {% endif %}
                            {% if service_data.recent_requests is defined %}
                                <div class="text-xs text-slate-500">{{ service_data.recent_requests }} requests (5m)</div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-8">
                    <p class="text-slate-600 dark:text-slate-400">Service status unavailable</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Metrics -->
    {% if status.metrics %}
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="card-title">System Metrics</h3>
                <p class="card-description">Current system performance metrics</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ "{:,}".format(status.metrics.total_users) if status.metrics.total_users else '0' }}
                    </div>
                    <div class="text-sm text-slate-600 dark:text-slate-400">Total Users</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ "{:,}".format(status.metrics.total_licenses) if status.metrics.total_licenses else '0' }}
                    </div>
                    <div class="text-sm text-slate-600 dark:text-slate-400">Total Licenses</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ "{:,}".format(status.metrics.validations_24h) if status.metrics.validations_24h else '0' }}
                    </div>
                    <div class="text-sm text-slate-600 dark:text-slate-400">Validations (24h)</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-slate-900 dark:text-white">
                        {{ status.metrics.avg_response_time_ms or '0' }}ms
                    </div>
                    <div class="text-sm text-slate-600 dark:text-slate-400">Avg Response Time</div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Incident History -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Recent Incidents</h3>
            <p class="card-description">Past 30 days</p>
        </div>
        
        <div class="space-y-4">
            <div class="flex items-start space-x-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div class="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
                <div class="flex-1">
                    <div class="flex items-center justify-between">
                        <h4 class="font-medium text-green-900 dark:text-green-100">No incidents reported</h4>
                        <span class="text-sm text-green-700 dark:text-green-300">Last 30 days</span>
                    </div>
                    <p class="text-sm text-green-800 dark:text-green-200 mt-1">
                        All services have been operating normally with no reported incidents.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscribe to Updates -->
    <div class="mt-8 text-center">
        <p class="text-slate-600 dark:text-slate-400 mb-4">
            Stay updated on system status and maintenance windows
        </p>
        <div class="flex justify-center space-x-4">
            <button class="btn-secondary" onclick="alert('Status notifications coming soon!')">
                <i data-lucide="bell" class="w-4 h-4 mr-2"></i>
                Subscribe to Updates
            </button>
            <a href="{{ url_for('status.api_status') }}" class="btn-ghost">
                <i data-lucide="code" class="w-4 h-4 mr-2"></i>
                API Status
            </a>
        </div>
    </div>
</div>

<script>
// Auto-refresh status every 30 seconds
setTimeout(() => {
    window.location.reload();
}, 30000);
</script>
{% endblock %}
