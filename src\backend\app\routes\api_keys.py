"""
API Key Management Routes
"""
from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, TextAreaField, SelectMultipleField
from wtforms.validators import DataRequired, Length

from app import db
from app.models.api_key import ApiKey
from app.models.audit_log import AuditLog

api_keys_bp = Blueprint('api_keys', __name__)


# Forms
class CreateApiKeyForm(FlaskForm):
    name = StringField('API Key Name', validators=[DataRequired(), Length(min=3, max=100)])
    permissions = SelectMultipleField('Permissions', choices=[
        ('validate_licenses', 'Validate Licenses'),
        ('manage_licenses', 'Manage Licenses'),
        ('view_analytics', 'View Analytics'),
        ('manage_webhooks', 'Manage Webhooks')
    ])
    notes = TextAreaField('Notes', validators=[Length(max=500)])
    submit = SubmitField('Create API Key')


@api_keys_bp.route('/')
@login_required
def list_api_keys():
    """List user's API keys"""
    api_keys = current_user.api_keys.order_by(ApiKey.created_at.desc()).all()
    return render_template('api_keys.html', api_keys=api_keys)


@api_keys_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_api_key():
    """Create a new API key"""
    form = CreateApiKeyForm()
    
    if form.validate_on_submit():
        try:
            # Create API key
            api_key = ApiKey(
                user_id=current_user.id,
                name=form.name.data,
                permissions={perm: True for perm in form.permissions.data}
            )
            api_key.save()
            
            # Log creation
            AuditLog.log_action(
                user_id=current_user.id,
                action='api_key_created',
                resource_type='api_key',
                resource_id=api_key.id,
                new_values={'name': api_key.name},
                ip_address=request.remote_addr
            )
            
            # Show the API key once
            flash(f'API Key created successfully! Please save this key: {api_key._plain_key}', 'success')
            return redirect(url_for('api_keys.list_api_keys'))
            
        except Exception as e:
            flash('Failed to create API key. Please try again.', 'error')
    
    return render_template('create_api_key.html', form=form)


@api_keys_bp.route('/<api_key_id>/revoke', methods=['POST'])
@login_required
def revoke_api_key(api_key_id):
    """Revoke an API key"""
    api_key = ApiKey.get_by_id(api_key_id)
    if not api_key or api_key.user_id != current_user.id:
        flash('API key not found', 'error')
        return redirect(url_for('api_keys.list_api_keys'))
    
    try:
        api_key.is_active = False
        api_key.save()
        
        # Log revocation
        AuditLog.log_action(
            user_id=current_user.id,
            action='api_key_revoked',
            resource_type='api_key',
            resource_id=api_key.id,
            old_values={'is_active': True},
            new_values={'is_active': False},
            ip_address=request.remote_addr
        )
        
        flash('API key revoked successfully', 'success')
        
    except Exception as e:
        flash('Failed to revoke API key', 'error')
    
    return redirect(url_for('api_keys.list_api_keys'))


# API endpoint for validating API keys
@api_keys_bp.route('/api/validate', methods=['POST'])
def validate_api_key():
    """Validate an API key - for internal use"""
    api_key = request.headers.get('X-API-Key')
    
    if not api_key:
        return jsonify({'valid': False, 'error': 'API key required'}), 401
    
    key_obj = ApiKey.get_by_key(api_key)
    if not key_obj or not key_obj.is_valid():
        return jsonify({'valid': False, 'error': 'Invalid or expired API key'}), 401
    
    # Record usage
    key_obj.record_usage()
    key_obj.save()
    
    return jsonify({
        'valid': True,
        'user_id': str(key_obj.user_id),
        'permissions': key_obj.permissions,
        'name': key_obj.name
    }), 200
