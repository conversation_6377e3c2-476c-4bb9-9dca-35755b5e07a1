"""
License Routes - Simplified Working Version
"""
import time
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import SelectField, StringField, IntegerField, DateTimeField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Optional, NumberRange

from app import db
from app.models.license import License, LicenseStatus, LicenseValidation, ValidationResult
from app.models.plan import Plan
from app.models.audit_log import AuditLog

licenses_bp = Blueprint('licenses', __name__)


# Forms
class CreateLicenseForm(FlaskForm):
    plan_id = SelectField('Plan', validators=[DataRequired()], coerce=str)
    max_devices = IntegerField('Max Devices', validators=[Optional(), NumberRange(min=1)], default=1)
    expires_at = DateTimeField('Expires At', validators=[Optional()], format='%Y-%m-%d')
    hwid_lock = StringField('Hardware ID Lock', validators=[Optional()])
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Create License')


# API Routes for License Validation
@licenses_bp.route('/api/validate', methods=['POST'])
def validate_license():
    """Validate a license key - Public API endpoint"""
    start_time = time.time()
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'valid': False,
                'result': 'invalid_request',
                'message': 'JSON data required'
            }), 400
        
        # Validate required fields
        required_fields = ['license_key', 'hwid']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'valid': False,
                    'result': 'invalid_key',
                    'message': f'{field} is required'
                }), 400
        
        license_key = data['license_key']
        hwid = data['hwid']
        
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        
        # Get license
        license_obj = License.query.filter_by(license_key=license_key).first()
        if not license_obj:
            _log_validation(license_key, hwid, ip_address, 'invalid_key', 'License not found', start_time)
            return jsonify({
                'valid': False,
                'result': 'invalid_key',
                'message': 'Invalid license key'
            }), 404
        
        # Check license status
        if license_obj.status == LicenseStatus.SUSPENDED:
            _log_validation(license_key, hwid, ip_address, 'suspended', 'License suspended', start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': 'suspended',
                'message': 'License is suspended'
            }), 403
        
        if license_obj.status == LicenseStatus.REVOKED:
            _log_validation(license_key, hwid, ip_address, 'invalid_key', 'License revoked', start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': 'invalid_key',
                'message': 'License is revoked'
            }), 403
        
        # Check expiration
        if license_obj.is_expired() and not license_obj.is_in_grace_period():
            _log_validation(license_key, hwid, ip_address, 'expired', 'License expired', start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': 'expired',
                'message': 'License has expired'
            }), 403
        
        # Check HWID lock
        if license_obj.hwid_lock and license_obj.hwid_lock != hwid:
            _log_validation(license_key, hwid, ip_address, 'hwid_mismatch', 'HWID mismatch', start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': 'hwid_mismatch',
                'message': 'Hardware ID mismatch'
            }), 403
        
        # Add/update device
        device = license_obj.add_device(hwid, ip_address=ip_address)
        if not device:
            _log_validation(license_key, hwid, ip_address, 'hwid_mismatch', 'Max devices exceeded', start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': 'hwid_mismatch',
                'message': 'Maximum devices exceeded'
            }), 403
        
        # Successful validation
        _log_validation(license_key, hwid, ip_address, 'success', 'Validation successful', start_time, license_obj.id)
        
        # Prepare response
        response_data = {
            'valid': True,
            'result': 'success',
            'message': 'License is valid',
            'expires_at': license_obj.expires_at.isoformat() if license_obj.expires_at else None,
            'plan': {
                'name': license_obj.plan.name,
                'type': license_obj.plan.type.value,
                'features': license_obj.plan.features
            }
        }
        
        return jsonify(response_data), 200
    
    except Exception as e:
        _log_validation(
            data.get('license_key', '') if 'data' in locals() else '', 
            data.get('hwid', '') if 'data' in locals() else '', 
            request.remote_addr, 
            'invalid_key',
            f"Internal error: {str(e)}", 
            start_time
        )
        return jsonify({
            'valid': False,
            'result': 'invalid_key',
            'message': 'Validation failed'
        }), 500


# Web Routes for License Management
@licenses_bp.route('/')
@login_required
def list_licenses():
    """List user licenses"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    licenses_query = current_user.licenses.order_by(License.created_at.desc())
    licenses_pagination = licenses_query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('licenses.html', 
                         licenses=licenses_pagination.items,
                         pagination=licenses_pagination)


@licenses_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_license():
    """Create a new license"""
    form = CreateLicenseForm()
    
    # Populate plan choices
    plans = Plan.get_active_plans()
    form.plan_id.choices = [(str(plan.id), f"{plan.name} - ${plan.price_monthly}/month") for plan in plans]
    
    if form.validate_on_submit():
        try:
            # Get plan
            plan = Plan.get_by_id(form.plan_id.data)
            if not plan:
                flash('Invalid plan selected', 'error')
                return render_template('create_license.html', form=form)
            
            # Create license
            license_obj = License(
                user_id=current_user.id,
                plan_id=plan.id,
                max_devices=form.max_devices.data or plan.max_devices_per_license or 1,
                expires_at=form.expires_at.data,
                hwid_lock=form.hwid_lock.data.strip() if form.hwid_lock.data else None
            )
            license_obj.save()
            
            # Log creation
            AuditLog.log_action(
                user_id=current_user.id,
                action='license_created',
                resource_type='license',
                resource_id=license_obj.id,
                new_values={'license_key': license_obj.license_key},
                ip_address=request.remote_addr
            )
            
            flash('License created successfully!', 'success')
            return redirect(url_for('licenses.list_licenses'))
            
        except Exception as e:
            flash('Failed to create license. Please try again.', 'error')
    
    return render_template('create_license.html', form=form)


@licenses_bp.route('/<license_id>')
@login_required
def view_license(license_id):
    """View license details"""
    license_obj = License.get_by_id(license_id)
    if not license_obj or license_obj.user_id != current_user.id:
        flash('License not found', 'error')
        return redirect(url_for('licenses.list_licenses'))
    
    # Get recent validations
    recent_validations = license_obj.validations.order_by(
        LicenseValidation.created_at.desc()
    ).limit(20).all()
    
    return render_template('license_detail.html', 
                         license=license_obj,
                         recent_validations=recent_validations)


@licenses_bp.route('/<license_id>/toggle-status', methods=['POST'])
@login_required
def toggle_license_status(license_id):
    """Toggle license status (suspend/activate)"""
    license_obj = License.get_by_id(license_id)
    if not license_obj or license_obj.user_id != current_user.id:
        flash('License not found', 'error')
        return redirect(url_for('licenses.list_licenses'))
    
    try:
        old_status = license_obj.status
        
        if license_obj.status == LicenseStatus.ACTIVE:
            license_obj.suspend("Suspended by user")
            flash('License suspended successfully', 'success')
        elif license_obj.status == LicenseStatus.SUSPENDED:
            license_obj.reactivate()
            flash('License reactivated successfully', 'success')
        else:
            flash('Cannot change status of this license', 'error')
            return redirect(url_for('licenses.view_license', license_id=license_id))
        
        license_obj.save()
        
        # Log status change
        AuditLog.log_action(
            user_id=current_user.id,
            action='license_status_changed',
            resource_type='license',
            resource_id=license_obj.id,
            old_values={'status': old_status.value},
            new_values={'status': license_obj.status.value},
            ip_address=request.remote_addr
        )
        
    except Exception as e:
        flash('Failed to update license status', 'error')
    
    return redirect(url_for('licenses.view_license', license_id=license_id))


def _log_validation(license_key, hwid, ip_address, result, error_message, start_time, license_id=None):
    """Log license validation attempt"""
    try:
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Convert string result to enum
        if isinstance(result, str):
            result_enum = ValidationResult.SUCCESS if result == 'success' else ValidationResult.INVALID_KEY
        else:
            result_enum = result
        
        validation = LicenseValidation(
            license_id=license_id,
            license_key=license_key,
            device_fingerprint=hwid,
            ip_address=ip_address,
            user_agent=request.headers.get('User-Agent', ''),
            result=result_enum,
            error_message=error_message if result != 'success' else None,
            response_time_ms=response_time_ms
        )
        validation.save()
    except Exception:
        pass  # Don't let logging errors break validation
