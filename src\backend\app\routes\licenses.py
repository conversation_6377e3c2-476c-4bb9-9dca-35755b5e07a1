"""
License Routes
"""
import time
from datetime import datetime
from flask import Blueprint, request, jsonify, g
import structlog

from app import db, limiter
from app.models.license import License, LicenseStatus, LicenseValidation, ValidationResult
from app.models.plan import Plan
from app.models.audit_log import AuditLog
from app.utils.auth import (
    jwt_required, auth_required, LicenseValidator, 
    get_client_ip, get_user_agent, get_country_from_ip
)

logger = structlog.get_logger()

licenses_bp = Blueprint('licenses', __name__)


@licenses_bp.route('/validate', methods=['POST'])
@limiter.limit("100 per minute")
def validate_license():
    """Validate a license key"""
    start_time = time.time()
    
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['license_key', 'hwid', 'timestamp', 'signature']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'valid': False,
                    'result': ValidationResult.INVALID_KEY.value,
                    'message': f'{field} is required'
                }), 400
        
        license_key = data['license_key']
        hwid = data['hwid']
        timestamp = data['timestamp']
        signature = data['signature']
        
        ip_address = get_client_ip()
        user_agent = get_user_agent()
        country = get_country_from_ip(ip_address)
        
        # Verify timestamp
        if not LicenseValidator.is_timestamp_valid(timestamp):
            result = ValidationResult.INVALID_KEY
            _log_validation(license_key, hwid, ip_address, country, user_agent, result, 
                          "Invalid timestamp", start_time)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Invalid timestamp'
            }), 400
        
        # Verify signature
        if not LicenseValidator.verify_signature(license_key, hwid, timestamp, signature):
            result = ValidationResult.INVALID_KEY
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "Invalid signature", start_time)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Invalid signature'
            }), 400
        
        # Get license
        license_obj = License.query.filter_by(license_key=license_key).first()
        if not license_obj:
            result = ValidationResult.INVALID_KEY
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "License not found", start_time)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Invalid license key'
            }), 404
        
        # Check license status
        if license_obj.status == LicenseStatus.SUSPENDED:
            result = ValidationResult.SUSPENDED
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "License suspended", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'License is suspended'
            }), 403
        
        if license_obj.status == LicenseStatus.REVOKED:
            result = ValidationResult.INVALID_KEY
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "License revoked", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'License is revoked'
            }), 403
        
        # Check expiration
        if license_obj.is_expired() and not license_obj.is_in_grace_period():
            result = ValidationResult.EXPIRED
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "License expired", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'License has expired'
            }), 403
        
        # Check IP restrictions
        if not license_obj.validate_ip(ip_address):
            result = ValidationResult.IP_BLOCKED
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "IP blocked", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'IP address is blocked'
            }), 403
        
        # Check country restrictions
        if not license_obj.validate_country(country):
            result = ValidationResult.IP_BLOCKED
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "Country blocked", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Country is blocked'
            }), 403
        
        # Check HWID lock
        if license_obj.hwid_lock and license_obj.hwid_lock != hwid:
            result = ValidationResult.HWID_MISMATCH
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "HWID mismatch", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Hardware ID mismatch'
            }), 403
        
        # Add/update device
        device = license_obj.add_device(hwid, ip_address=ip_address, country=country)
        if not device:
            result = ValidationResult.HWID_MISMATCH
            _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                          "Max devices exceeded", start_time, license_obj.id)
            return jsonify({
                'valid': False,
                'result': result.value,
                'message': 'Maximum devices exceeded'
            }), 403
        
        # Successful validation
        result = ValidationResult.SUCCESS
        _log_validation(license_key, hwid, ip_address, country, user_agent, result,
                      "Validation successful", start_time, license_obj.id)
        
        # Prepare response
        response_data = {
            'valid': True,
            'result': result.value,
            'message': 'License is valid',
            'expires_at': license_obj.expires_at.isoformat() if license_obj.expires_at else None,
            'plan': {
                'name': license_obj.plan.name,
                'type': license_obj.plan.type.value,
                'features': license_obj.plan.features
            }
        }
        
        # Add CDN token if plan supports it
        if license_obj.plan.has_feature('cdn_access'):
            # Generate CDN token (placeholder)
            response_data['cdn_token'] = f"cdn_token_{license_obj.id}"
        
        # Add update manifest if plan supports it
        if license_obj.plan.has_feature('auto_updates'):
            response_data['update_manifest'] = {
                'version': '1.0.0',
                'download_url': 'https://cdn.pepe-auth.com/updates/latest.zip'
            }
        
        return jsonify(response_data), 200
    
    except Exception as e:
        logger.error("License validation failed", error=str(e))
        result = ValidationResult.INVALID_KEY
        _log_validation(
            data.get('license_key', ''), 
            data.get('hwid', ''), 
            get_client_ip(), 
            get_country_from_ip(get_client_ip()), 
            get_user_agent(), 
            result,
            f"Internal error: {str(e)}", 
            start_time
        )
        return jsonify({
            'valid': False,
            'result': result.value,
            'message': 'Validation failed'
        }), 500


@licenses_bp.route('', methods=['GET'])
@auth_required()
def list_licenses():
    """List user licenses"""
    try:
        # Get query parameters
        status = request.args.get('status')
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        
        # Build query
        query = g.current_user.licenses
        
        if status:
            try:
                status_enum = LicenseStatus(status)
                query = query.filter_by(status=status_enum)
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
        
        # Get total count
        total = query.count()
        
        # Get licenses with pagination
        licenses = query.offset(offset).limit(limit).all()
        
        return jsonify({
            'licenses': [license.to_dict() for license in licenses],
            'total': total,
            'limit': limit,
            'offset': offset
        }), 200
    
    except Exception as e:
        logger.error("List licenses failed", error=str(e))
        return jsonify({'error': 'Failed to list licenses'}), 500


@licenses_bp.route('', methods=['POST'])
@auth_required()
def create_license():
    """Create a new license"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('plan_id'):
            return jsonify({'error': 'plan_id is required'}), 400
        
        # Get plan
        plan = Plan.get_by_id(data['plan_id'])
        if not plan or not plan.is_active:
            return jsonify({'error': 'Invalid plan'}), 400
        
        # Check if user can create license with this plan
        if not plan.can_create_license(g.current_user):
            return jsonify({'error': 'License limit exceeded for this plan'}), 400
        
        # Create license
        license_data = {
            'user_id': g.current_user.id,
            'plan_id': plan.id,
            'max_devices': data.get('max_devices', plan.max_devices_per_license or 1),
            'expires_at': data.get('expires_at'),
            'hwid_lock': data.get('hwid_lock')
        }
        
        license_obj = License.create(**license_data)
        
        # Log creation
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='license_created',
            resource_type='license',
            resource_id=license_obj.id,
            new_values=license_obj.to_dict(),
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        logger.info("License created", license_id=str(license_obj.id), user_id=str(g.current_user.id))
        
        return jsonify({
            'message': 'License created successfully',
            'license': license_obj.to_dict()
        }), 201
    
    except Exception as e:
        logger.error("License creation failed", error=str(e))
        return jsonify({'error': 'Failed to create license'}), 500


@licenses_bp.route('/<license_id>', methods=['GET'])
@auth_required()
def get_license(license_id):
    """Get license details"""
    try:
        license_obj = License.get_by_id(license_id)
        if not license_obj or license_obj.user_id != g.current_user.id:
            return jsonify({'error': 'License not found'}), 404
        
        return jsonify({
            'license': license_obj.to_dict()
        }), 200
    
    except Exception as e:
        logger.error("Get license failed", error=str(e))
        return jsonify({'error': 'Failed to get license'}), 500


@licenses_bp.route('/<license_id>', methods=['PATCH'])
@auth_required()
def update_license(license_id):
    """Update license"""
    try:
        license_obj = License.get_by_id(license_id)
        if not license_obj or license_obj.user_id != g.current_user.id:
            return jsonify({'error': 'License not found'}), 404
        
        data = request.get_json()
        old_values = license_obj.to_dict()
        
        # Update allowed fields
        updatable_fields = ['status', 'hwid_lock', 'max_devices', 'expires_at']
        for field in updatable_fields:
            if field in data:
                if field == 'status':
                    try:
                        status = LicenseStatus(data[field])
                        license_obj.status = status
                    except ValueError:
                        return jsonify({'error': 'Invalid status value'}), 400
                else:
                    setattr(license_obj, field, data[field])
        
        license_obj.save()
        
        # Log update
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='license_updated',
            resource_type='license',
            resource_id=license_obj.id,
            old_values=old_values,
            new_values=license_obj.to_dict(),
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        return jsonify({
            'message': 'License updated successfully',
            'license': license_obj.to_dict()
        }), 200
    
    except Exception as e:
        logger.error("License update failed", error=str(e))
        return jsonify({'error': 'Failed to update license'}), 500


@licenses_bp.route('/<license_id>', methods=['DELETE'])
@auth_required()
def revoke_license(license_id):
    """Revoke license"""
    try:
        license_obj = License.get_by_id(license_id)
        if not license_obj or license_obj.user_id != g.current_user.id:
            return jsonify({'error': 'License not found'}), 404
        
        old_values = license_obj.to_dict()
        license_obj.revoke("Revoked by user")
        license_obj.save()
        
        # Log revocation
        AuditLog.log_action(
            user_id=g.current_user.id,
            action='license_revoked',
            resource_type='license',
            resource_id=license_obj.id,
            old_values=old_values,
            new_values=license_obj.to_dict(),
            ip_address=get_client_ip(),
            user_agent=get_user_agent()
        )
        
        return jsonify({'message': 'License revoked successfully'}), 204
    
    except Exception as e:
        logger.error("License revocation failed", error=str(e))
        return jsonify({'error': 'Failed to revoke license'}), 500


def _log_validation(license_key, hwid, ip_address, country, user_agent, result, error_message, start_time, license_id=None):
    """Log license validation attempt"""
    try:
        response_time_ms = int((time.time() - start_time) * 1000)
        
        validation = LicenseValidation(
            license_id=license_id,
            license_key=license_key,
            device_fingerprint=hwid,
            ip_address=ip_address,
            country=country,
            user_agent=user_agent,
            result=result,
            error_message=error_message if result != ValidationResult.SUCCESS else None,
            response_time_ms=response_time_ms
        )
        validation.save()
    except Exception as e:
        logger.error("Failed to log validation", error=str(e))
