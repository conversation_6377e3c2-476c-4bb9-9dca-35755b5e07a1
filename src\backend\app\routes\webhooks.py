"""
Webhook Management Routes
"""
import requests
import json
import hmac
import hashlib
from datetime import datetime
from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField, TextAreaField, SelectMultipleField, URLField
from wtforms.validators import DataRequired, Length, URL

from app import db
from app.models.webhook import Webhook, WebhookDelivery
from app.models.audit_log import AuditLog

webhooks_bp = Blueprint('webhooks', __name__)


# Forms
class CreateWebhookForm(FlaskForm):
    name = StringField('Webhook Name', validators=[DataRequired(), Length(min=3, max=100)])
    url = URLField('Webhook URL', validators=[DataRequired(), URL()])
    events = SelectMultipleField('Events', choices=[
        ('license.created', 'License Created'),
        ('license.validated', 'License Validated'),
        ('license.expired', 'License Expired'),
        ('license.suspended', 'License Suspended'),
        ('license.revoked', 'License Revoked'),
        ('user.registered', 'User Registered'),
        ('payment.succeeded', 'Payment Succeeded'),
        ('payment.failed', 'Payment Failed')
    ], validators=[DataRequired()])
    description = TextAreaField('Description', validators=[Length(max=500)])
    submit = SubmitField('Create Webhook')


@webhooks_bp.route('/')
@login_required
def list_webhooks():
    """List user's webhooks"""
    webhooks = current_user.webhooks.order_by(Webhook.created_at.desc()).all()
    return render_template('webhooks.html', webhooks=webhooks)


@webhooks_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_webhook():
    """Create a new webhook"""
    form = CreateWebhookForm()
    
    if form.validate_on_submit():
        try:
            # Create webhook
            webhook = Webhook(
                user_id=current_user.id,
                name=form.name.data,
                url=form.url.data,
                events=form.events.data
            )
            webhook.save()
            
            # Log creation
            AuditLog.log_action(
                user_id=current_user.id,
                action='webhook_created',
                resource_type='webhook',
                resource_id=webhook.id,
                new_values={'name': webhook.name, 'url': webhook.url},
                ip_address=request.remote_addr
            )
            
            flash('Webhook created successfully!', 'success')
            return redirect(url_for('webhooks.list_webhooks'))
            
        except Exception as e:
            flash('Failed to create webhook. Please try again.', 'error')
    
    return render_template('create_webhook.html', form=form)


@webhooks_bp.route('/<webhook_id>')
@login_required
def view_webhook(webhook_id):
    """View webhook details"""
    webhook = Webhook.get_by_id(webhook_id)
    if not webhook or webhook.user_id != current_user.id:
        flash('Webhook not found', 'error')
        return redirect(url_for('webhooks.list_webhooks'))
    
    # Get recent deliveries
    recent_deliveries = webhook.deliveries.order_by(
        WebhookDelivery.created_at.desc()
    ).limit(20).all()
    
    return render_template('webhook_detail.html', 
                         webhook=webhook,
                         recent_deliveries=recent_deliveries)


@webhooks_bp.route('/<webhook_id>/toggle', methods=['POST'])
@login_required
def toggle_webhook(webhook_id):
    """Toggle webhook active status"""
    webhook = Webhook.get_by_id(webhook_id)
    if not webhook or webhook.user_id != current_user.id:
        flash('Webhook not found', 'error')
        return redirect(url_for('webhooks.list_webhooks'))
    
    try:
        old_status = webhook.is_active
        webhook.is_active = not webhook.is_active
        webhook.save()
        
        # Log status change
        AuditLog.log_action(
            user_id=current_user.id,
            action='webhook_toggled',
            resource_type='webhook',
            resource_id=webhook.id,
            old_values={'is_active': old_status},
            new_values={'is_active': webhook.is_active},
            ip_address=request.remote_addr
        )
        
        status = 'enabled' if webhook.is_active else 'disabled'
        flash(f'Webhook {status} successfully', 'success')
        
    except Exception as e:
        flash('Failed to update webhook status', 'error')
    
    return redirect(url_for('webhooks.view_webhook', webhook_id=webhook_id))


@webhooks_bp.route('/<webhook_id>/test', methods=['POST'])
@login_required
def test_webhook(webhook_id):
    """Test webhook delivery"""
    webhook = Webhook.get_by_id(webhook_id)
    if not webhook or webhook.user_id != current_user.id:
        flash('Webhook not found', 'error')
        return redirect(url_for('webhooks.list_webhooks'))
    
    try:
        # Create test payload
        test_payload = {
            'event': 'webhook.test',
            'timestamp': datetime.utcnow().isoformat(),
            'data': {
                'webhook_id': str(webhook.id),
                'webhook_name': webhook.name,
                'message': 'This is a test webhook delivery'
            }
        }
        
        # Send webhook
        success = send_webhook(webhook, test_payload)
        
        if success:
            flash('Test webhook sent successfully!', 'success')
        else:
            flash('Test webhook failed to send', 'error')
            
    except Exception as e:
        flash('Failed to send test webhook', 'error')
    
    return redirect(url_for('webhooks.view_webhook', webhook_id=webhook_id))


@webhooks_bp.route('/<webhook_id>/delete', methods=['POST'])
@login_required
def delete_webhook(webhook_id):
    """Delete webhook"""
    webhook = Webhook.get_by_id(webhook_id)
    if not webhook or webhook.user_id != current_user.id:
        flash('Webhook not found', 'error')
        return redirect(url_for('webhooks.list_webhooks'))
    
    try:
        webhook_name = webhook.name
        
        # Log deletion
        AuditLog.log_action(
            user_id=current_user.id,
            action='webhook_deleted',
            resource_type='webhook',
            resource_id=webhook.id,
            old_values={'name': webhook.name, 'url': webhook.url},
            ip_address=request.remote_addr
        )
        
        webhook.delete()
        flash(f'Webhook "{webhook_name}" deleted successfully', 'success')
        
    except Exception as e:
        flash('Failed to delete webhook', 'error')
    
    return redirect(url_for('webhooks.list_webhooks'))


def send_webhook(webhook, payload):
    """Send webhook payload to endpoint"""
    try:
        # Create signature
        payload_json = json.dumps(payload, sort_keys=True)
        signature = hmac.new(
            webhook.secret.encode(),
            payload_json.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Send request
        headers = {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': f'sha256={signature}',
            'X-Webhook-Event': payload.get('event', 'unknown'),
            'User-Agent': 'Pepe-Auth-Webhooks/1.0'
        }
        
        response = requests.post(
            webhook.url,
            data=payload_json,
            headers=headers,
            timeout=10
        )
        
        # Log delivery
        delivery = WebhookDelivery(
            webhook_id=webhook.id,
            event_type=payload.get('event', 'unknown'),
            payload=payload,
            response_status=response.status_code,
            response_body=response.text[:1000],  # Limit response body
            delivered_at=datetime.utcnow() if response.status_code < 400 else None
        )
        delivery.save()
        
        # Update webhook stats
        if response.status_code >= 400:
            webhook.failure_count += 1
        else:
            webhook.failure_count = 0
            webhook.last_triggered_at = datetime.utcnow()
        
        webhook.save()
        
        return response.status_code < 400
        
    except Exception as e:
        # Log failed delivery
        delivery = WebhookDelivery(
            webhook_id=webhook.id,
            event_type=payload.get('event', 'unknown'),
            payload=payload,
            response_status=0,
            response_body=str(e)[:1000],
            delivered_at=None
        )
        delivery.save()
        
        webhook.failure_count += 1
        webhook.save()
        
        return False


def trigger_webhook_event(user_id, event_type, data):
    """Trigger webhook event for user"""
    try:
        # Get user's webhooks that listen for this event
        webhooks = Webhook.query.filter(
            Webhook.user_id == user_id,
            Webhook.is_active == True,
            Webhook.events.contains([event_type])
        ).all()
        
        if not webhooks:
            return
        
        # Create payload
        payload = {
            'event': event_type,
            'timestamp': datetime.utcnow().isoformat(),
            'data': data
        }
        
        # Send to all matching webhooks
        for webhook in webhooks:
            send_webhook(webhook, payload)
            
    except Exception:
        pass  # Don't let webhook failures break the main flow
