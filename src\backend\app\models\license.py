"""
License Models
"""
import enum
import secrets
import string
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import Column, String, Boolean, Integer, DateTime, Enum, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, INET, ARRAY, JSONB
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class LicenseStatus(enum.Enum):
    """License status enumeration"""
    ACTIVE = 'active'
    EXPIRED = 'expired'
    SUSPENDED = 'suspended'
    REVOKED = 'revoked'


class ValidationResult(enum.Enum):
    """Validation result enumeration"""
    SUCCESS = 'success'
    INVALID_KEY = 'invalid_key'
    EXPIRED = 'expired'
    SUSPENDED = 'suspended'
    HWID_MISMATCH = 'hwid_mismatch'
    IP_BLOCKED = 'ip_blocked'
    RATE_LIMITED = 'rate_limited'


class License(BaseModel):
    """License model for software licensing"""
    
    __tablename__ = 'licenses'
    
    # Relationships
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    plan_id = Column(UUID(as_uuid=True), ForeignKey('plans.id'), nullable=False)
    
    # License details
    license_key = Column(String(255), unique=True, nullable=False, index=True)
    license_signature = Column(String(512), nullable=False)
    status = Column(Enum(LicenseStatus), default=LicenseStatus.ACTIVE, nullable=False)
    
    # Hardware and IP restrictions
    hwid_lock = Column(String(255))
    ip_whitelist = Column(ARRAY(INET))
    ip_blacklist = Column(ARRAY(INET))
    country_whitelist = Column(ARRAY(String(2)))
    country_blacklist = Column(ARRAY(String(2)))
    
    # Device management
    max_devices = Column(Integer, default=1, nullable=False)
    current_devices = Column(Integer, default=0, nullable=False)
    
    # Expiration
    expires_at = Column(DateTime)
    grace_period_days = Column(Integer, default=7, nullable=False)
    
    # Metadata
    metadata = Column(JSONB, default={})
    
    # Relationships
    user = relationship('User', back_populates='licenses')
    plan = relationship('Plan', back_populates='licenses')
    devices = relationship('LicenseDevice', back_populates='license', lazy='dynamic', cascade='all, delete-orphan')
    validations = relationship('LicenseValidation', back_populates='license', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        """Initialize license with auto-generated key"""
        if 'license_key' not in kwargs:
            kwargs['license_key'] = self.generate_license_key()
        super().__init__(**kwargs)
    
    @staticmethod
    def generate_license_key(format_type='standard'):
        """Generate a license key"""
        if format_type == 'standard':
            # Format: XXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
            chars = string.ascii_uppercase + string.digits
            parts = [
                ''.join(secrets.choice(chars) for _ in range(4)),
                ''.join(secrets.choice(chars) for _ in range(4)),
                ''.join(secrets.choice(chars) for _ in range(4)),
                ''.join(secrets.choice(chars) for _ in range(4)),
                ''.join(secrets.choice(chars) for _ in range(12))
            ]
            return '-'.join(parts)
        else:
            # Simple format: 32 character string
            chars = string.ascii_uppercase + string.digits
            return ''.join(secrets.choice(chars) for _ in range(32))
    
    def is_valid(self):
        """Check if license is valid"""
        if self.status != LicenseStatus.ACTIVE:
            return False
        
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        
        return True
    
    def is_expired(self):
        """Check if license is expired"""
        if not self.expires_at:
            return False
        return self.expires_at < datetime.utcnow()
    
    def is_in_grace_period(self):
        """Check if license is in grace period"""
        if not self.expires_at:
            return False
        
        grace_end = self.expires_at + timedelta(days=self.grace_period_days)
        now = datetime.utcnow()
        return self.expires_at < now < grace_end
    
    def can_add_device(self):
        """Check if a new device can be added"""
        return self.current_devices < self.max_devices
    
    def add_device(self, device_fingerprint, device_name=None, ip_address=None, country=None):
        """Add a new device to the license"""
        if not self.can_add_device():
            return None
        
        # Check if device already exists
        existing_device = self.devices.filter_by(device_fingerprint=device_fingerprint).first()
        if existing_device:
            existing_device.last_seen_at = datetime.utcnow()
            existing_device.last_ip = ip_address
            existing_device.last_country = country
            existing_device.is_active = True
            return existing_device
        
        # Create new device
        device = LicenseDevice(
            license_id=self.id,
            device_fingerprint=device_fingerprint,
            device_name=device_name,
            last_ip=ip_address,
            last_country=country
        )
        device.save()
        
        self.current_devices += 1
        self.save()
        
        return device
    
    def remove_device(self, device_fingerprint):
        """Remove a device from the license"""
        device = self.devices.filter_by(device_fingerprint=device_fingerprint).first()
        if device:
            device.is_active = False
            self.current_devices = max(0, self.current_devices - 1)
            self.save()
            return True
        return False
    
    def validate_ip(self, ip_address):
        """Validate IP address against whitelist/blacklist"""
        if self.ip_blacklist and ip_address in self.ip_blacklist:
            return False
        
        if self.ip_whitelist and ip_address not in self.ip_whitelist:
            return False
        
        return True
    
    def validate_country(self, country_code):
        """Validate country against whitelist/blacklist"""
        if self.country_blacklist and country_code in self.country_blacklist:
            return False
        
        if self.country_whitelist and country_code not in self.country_whitelist:
            return False
        
        return True
    
    def suspend(self, reason=None):
        """Suspend the license"""
        self.status = LicenseStatus.SUSPENDED
        if reason:
            self.metadata = self.metadata or {}
            self.metadata['suspension_reason'] = reason
            self.metadata['suspended_at'] = datetime.utcnow().isoformat()
    
    def revoke(self, reason=None):
        """Revoke the license"""
        self.status = LicenseStatus.REVOKED
        if reason:
            self.metadata = self.metadata or {}
            self.metadata['revocation_reason'] = reason
            self.metadata['revoked_at'] = datetime.utcnow().isoformat()
    
    def reactivate(self):
        """Reactivate a suspended license"""
        if self.status == LicenseStatus.SUSPENDED:
            self.status = LicenseStatus.ACTIVE
            if self.metadata:
                self.metadata.pop('suspension_reason', None)
                self.metadata.pop('suspended_at', None)
    
    def extend_expiration(self, days):
        """Extend license expiration by specified days"""
        if self.expires_at:
            self.expires_at += timedelta(days=days)
        else:
            self.expires_at = datetime.utcnow() + timedelta(days=days)
    
    def to_dict(self, exclude=None):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict(exclude=exclude)
        data['status'] = self.status.value if self.status else None
        data['is_valid'] = self.is_valid()
        data['is_expired'] = self.is_expired()
        data['is_in_grace_period'] = self.is_in_grace_period()
        data['can_add_device'] = self.can_add_device()
        data['plan'] = self.plan.to_dict() if self.plan else None
        return data
    
    def __repr__(self):
        return f'<License {self.license_key}>'


class LicenseDevice(BaseModel):
    """Device associated with a license"""
    
    __tablename__ = 'license_devices'
    
    # Relationships
    license_id = Column(UUID(as_uuid=True), ForeignKey('licenses.id'), nullable=False)
    
    # Device information
    device_fingerprint = Column(String(255), nullable=False)
    device_name = Column(String(255))
    
    # Tracking
    first_seen_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_seen_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_ip = Column(INET)
    last_country = Column(String(2))
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    license = relationship('License', back_populates='devices')
    
    def to_dict(self, exclude=None):
        """Convert to dictionary"""
        data = super().to_dict(exclude=exclude)
        return data
    
    def __repr__(self):
        return f'<LicenseDevice {self.device_fingerprint}>'


class LicenseValidation(BaseModel):
    """License validation attempt record"""
    
    __tablename__ = 'license_validations'
    
    # Relationships
    license_id = Column(UUID(as_uuid=True), ForeignKey('licenses.id'))
    
    # Validation details
    license_key = Column(String(255), nullable=False)
    device_fingerprint = Column(String(255))
    ip_address = Column(INET)
    country = Column(String(2))
    user_agent = Column(Text)
    
    # Result
    result = Column(Enum(ValidationResult), nullable=False)
    error_message = Column(Text)
    response_time_ms = Column(Integer)
    
    # Relationships
    license = relationship('License', back_populates='validations')
    
    def to_dict(self, exclude=None):
        """Convert to dictionary"""
        data = super().to_dict(exclude=exclude)
        data['result'] = self.result.value if self.result else None
        return data
    
    def __repr__(self):
        return f'<LicenseValidation {self.license_key} - {self.result.value}>'
