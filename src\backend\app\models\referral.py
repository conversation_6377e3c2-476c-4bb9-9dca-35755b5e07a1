"""
Referral Model
"""
import secrets
import string
from decimal import Decimal
from datetime import datetime
from sqlalchemy import Column, String, Numeric, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class Referral(BaseModel):
    """Referral model for tracking referrals"""
    
    __tablename__ = 'referrals'
    
    # Relationships
    referrer_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    referee_id = Column(UUID(as_uuid=True), ForeignKey('users.id'))
    
    # Referral details
    referral_code = Column(String(20), unique=True, nullable=False)
    email = Column(String(255))
    commission_rate = Column(Numeric(5, 2), default=Decimal('10.00'), nullable=False)
    total_earned = Column(Numeric(10, 2), default=Decimal('0.00'), nullable=False)
    status = Column(String(20), default='pending', nullable=False)
    converted_at = Column(DateTime)
    
    # Relationships
    referrer = relationship('User', foreign_keys=[referrer_id], back_populates='referrals_made')
    referee = relationship('User', foreign_keys=[referee_id], back_populates='referrals_received')
    
    def __init__(self, **kwargs):
        """Initialize referral with auto-generated code"""
        if 'referral_code' not in kwargs:
            kwargs['referral_code'] = self.generate_referral_code()
        super().__init__(**kwargs)
    
    @staticmethod
    def generate_referral_code():
        """Generate a unique referral code"""
        chars = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(chars) for _ in range(8))
    
    def convert(self, referee_user):
        """Convert referral when referee signs up"""
        self.referee_id = referee_user.id
        self.status = 'converted'
        self.converted_at = datetime.utcnow()
    
    def __repr__(self):
        return f'<Referral {self.referral_code}>'
