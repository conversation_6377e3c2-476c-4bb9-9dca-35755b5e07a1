"""
Plan Model
"""
import enum
from decimal import Decimal
from sqlalchemy import Column, String, Text, Boolean, Integer, Numeric, Enum, JSON
from sqlalchemy.orm import relationship

from app import db
from .base import BaseModel


class PlanType(enum.Enum):
    """Plan type enumeration"""
    FREE_TRIAL = 'free_trial'
    HOBBY = 'hobby'
    PRO = 'pro'
    ENTERPRISE = 'enterprise'


class Plan(BaseModel):
    """Subscription plan model"""
    
    __tablename__ = 'plans'
    
    # Basic information
    name = Column(String(100), nullable=False)
    type = Column(Enum(PlanType), nullable=False)
    description = Column(Text)
    
    # Pricing
    price_monthly = Column(Numeric(10, 2))
    price_yearly = Column(Numeric(10, 2))
    
    # Limits
    max_licenses = Column(Integer)  # -1 for unlimited
    max_devices_per_license = Column(Integer)  # -1 for unlimited
    max_api_calls_per_month = Column(Integer)  # -1 for unlimited
    
    # Features (JSON object)
    features = Column(JSON, default={})
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    licenses = relationship('License', back_populates='plan', lazy='dynamic')
    
    def __init__(self, **kwargs):
        """Initialize plan with default features"""
        super().__init__(**kwargs)
        if not self.features:
            self.features = self._get_default_features()
    
    def _get_default_features(self):
        """Get default features based on plan type"""
        default_features = {
            PlanType.FREE_TRIAL: {
                'trial_days': 30,
                'support': 'community',
                'analytics': 'basic',
                'webhooks': False,
                'api_access': False,
                'white_label': False,
                'sso': False,
                'priority_support': False,
                'custom_domains': False,
                'advanced_analytics': False,
                'fraud_detection': False,
                'offline_licenses': True,
                'geofencing': False,
                'rate_limiting': 'basic'
            },
            PlanType.HOBBY: {
                'support': 'email',
                'analytics': 'basic',
                'webhooks': True,
                'api_access': True,
                'white_label': False,
                'sso': False,
                'priority_support': False,
                'custom_domains': False,
                'advanced_analytics': False,
                'fraud_detection': False,
                'offline_licenses': True,
                'geofencing': True,
                'rate_limiting': 'standard'
            },
            PlanType.PRO: {
                'support': 'priority',
                'analytics': 'advanced',
                'webhooks': True,
                'api_access': True,
                'white_label': True,
                'sso': False,
                'priority_support': True,
                'custom_domains': True,
                'advanced_analytics': True,
                'fraud_detection': True,
                'offline_licenses': True,
                'geofencing': True,
                'rate_limiting': 'enhanced'
            },
            PlanType.ENTERPRISE: {
                'support': 'dedicated',
                'analytics': 'premium',
                'webhooks': True,
                'api_access': True,
                'white_label': True,
                'sso': True,
                'priority_support': True,
                'custom_domains': True,
                'advanced_analytics': True,
                'fraud_detection': True,
                'offline_licenses': True,
                'geofencing': True,
                'rate_limiting': 'unlimited',
                'custom_integrations': True,
                'dedicated_support': True,
                'sla_guarantee': True
            }
        }
        return default_features.get(self.type, {})
    
    def has_feature(self, feature_name):
        """Check if plan has a specific feature"""
        return self.features.get(feature_name, False)
    
    def get_feature_value(self, feature_name, default=None):
        """Get feature value with default"""
        return self.features.get(feature_name, default)
    
    def is_unlimited_licenses(self):
        """Check if plan has unlimited licenses"""
        return self.max_licenses == -1
    
    def is_unlimited_devices(self):
        """Check if plan has unlimited devices per license"""
        return self.max_devices_per_license == -1
    
    def is_unlimited_api_calls(self):
        """Check if plan has unlimited API calls"""
        return self.max_api_calls_per_month == -1
    
    def get_yearly_discount_percentage(self):
        """Calculate yearly discount percentage"""
        if not self.price_monthly or not self.price_yearly:
            return 0
        
        monthly_yearly = self.price_monthly * 12
        discount = monthly_yearly - self.price_yearly
        return round((discount / monthly_yearly) * 100, 1)
    
    def can_create_license(self, user):
        """Check if user can create a new license with this plan"""
        if self.is_unlimited_licenses():
            return True
        
        current_licenses = user.licenses.filter_by(plan_id=self.id).count()
        return current_licenses < self.max_licenses
    
    def get_limits_summary(self):
        """Get a summary of plan limits"""
        return {
            'licenses': 'Unlimited' if self.is_unlimited_licenses() else self.max_licenses,
            'devices_per_license': 'Unlimited' if self.is_unlimited_devices() else self.max_devices_per_license,
            'api_calls_per_month': 'Unlimited' if self.is_unlimited_api_calls() else f"{self.max_api_calls_per_month:,}"
        }
    
    def to_dict(self, exclude=None):
        """Convert to dictionary with additional computed fields"""
        data = super().to_dict(exclude=exclude)
        data['type'] = self.type.value if self.type else None
        data['price_monthly'] = float(self.price_monthly) if self.price_monthly else None
        data['price_yearly'] = float(self.price_yearly) if self.price_yearly else None
        data['yearly_discount_percentage'] = self.get_yearly_discount_percentage()
        data['limits_summary'] = self.get_limits_summary()
        return data
    
    @classmethod
    def get_active_plans(cls):
        """Get all active plans"""
        return cls.query.filter_by(is_active=True).all()
    
    @classmethod
    def get_by_type(cls, plan_type):
        """Get plan by type"""
        if isinstance(plan_type, str):
            plan_type = PlanType(plan_type)
        return cls.query.filter_by(type=plan_type, is_active=True).first()
    
    @classmethod
    def get_free_trial_plan(cls):
        """Get the free trial plan"""
        return cls.get_by_type(PlanType.FREE_TRIAL)
    
    def __repr__(self):
        return f'<Plan {self.name}>'
