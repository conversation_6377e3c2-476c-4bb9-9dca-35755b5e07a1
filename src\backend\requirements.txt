# Core Flask dependencies
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
Flask-Login==0.6.3
Flask-RESTful==0.3.10
Flask-CORS==4.0.0
Flask-Limiter==3.5.0

# Database
psycopg2-binary==2.9.9
redis==5.0.1

# Authentication & Security
PyJWT==2.8.0
argon2-cffi==23.1.0
cryptography==41.0.8
pyotp==2.9.0
qrcode==7.4.2

# API & Serialization
marshmallow==3.20.2
marshmallow-sqlalchemy==0.29.0
apispec==6.3.0
apispec-webframeworks==0.5.2

# Utilities
python-dotenv==1.0.0
click==8.1.7
requests==2.31.0
celery==5.3.4
gunicorn==21.2.0

# Development & Testing
pytest==7.4.3
pytest-cov==4.1.0
pytest-flask==1.3.0
factory-boy==3.3.0
faker==20.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Monitoring & Logging
structlog==23.2.0
sentry-sdk[flask]==1.38.0

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# Email
Flask-Mail==0.9.1

# File handling
Pillow==10.1.0

# Environment
environs==10.3.0
