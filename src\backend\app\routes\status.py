"""
Status and Health Check Routes
"""
import time
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, render_template
from sqlalchemy import text

from app import db
from app.models.license import License, LicenseValidation
from app.models.user import User

status_bp = Blueprint('status', __name__)


@status_bp.route('/')
def status_page():
    """Public status page"""
    try:
        # Get system status
        status_data = get_system_status()
        return render_template('status.html', status=status_data)
    except Exception:
        return render_template('status.html', status={'overall': 'degraded'})


@status_bp.route('/api')
def api_status():
    """API status endpoint"""
    try:
        status_data = get_system_status()
        return jsonify(status_data), 200
    except Exception as e:
        return jsonify({
            'overall': 'down',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503


def get_system_status():
    """Get comprehensive system status"""
    start_time = time.time()
    
    status = {
        'overall': 'operational',
        'timestamp': datetime.utcnow().isoformat(),
        'services': {},
        'metrics': {},
        'uptime': '99.9%'  # Placeholder
    }
    
    # Database status
    try:
        db.session.execute(text('SELECT 1'))
        db_response_time = (time.time() - start_time) * 1000
        status['services']['database'] = {
            'status': 'operational',
            'response_time_ms': round(db_response_time, 2)
        }
    except Exception as e:
        status['services']['database'] = {
            'status': 'down',
            'error': str(e)
        }
        status['overall'] = 'down'
    
    # License validation API status
    try:
        # Check recent validations
        recent_validations = LicenseValidation.query.filter(
            LicenseValidation.created_at >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        
        status['services']['license_validation'] = {
            'status': 'operational',
            'recent_requests': recent_validations
        }
    except Exception as e:
        status['services']['license_validation'] = {
            'status': 'degraded',
            'error': str(e)
        }
        if status['overall'] == 'operational':
            status['overall'] = 'degraded'
    
    # Get metrics
    try:
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        
        status['metrics'] = {
            'total_users': User.query.count(),
            'total_licenses': License.query.count(),
            'active_licenses': License.query.filter_by(status='active').count(),
            'validations_24h': LicenseValidation.query.filter(
                LicenseValidation.created_at >= last_24h
            ).count(),
            'avg_response_time_ms': 45  # Placeholder
        }
    except Exception:
        status['metrics'] = {}
    
    return status
