@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900;
    @apply text-slate-900 dark:text-slate-100;
    @apply font-sans antialiased;
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glass morphism utilities */
  .glass-surface {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-glass;
  }

  .glass-surface-dark {
    @apply bg-slate-900/30 backdrop-blur-lg border border-slate-400/10 shadow-glass-dark;
  }

  .glass-card {
    @apply glass-surface rounded-2xl p-6 transition-all duration-200;
  }

  .glass-card:hover {
    @apply transform -translate-y-1 shadow-xl;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-blue-600 text-white font-semibold py-3 px-6 rounded-xl;
    @apply backdrop-blur-sm border border-white/20 shadow-lg;
    @apply transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/50;
  }

  .btn-secondary {
    @apply bg-white/10 backdrop-blur-sm text-slate-700 dark:text-slate-200 font-medium py-3 px-6 rounded-xl;
    @apply border border-white/20 shadow-md;
    @apply transition-all duration-200 hover:bg-white/20 hover:-translate-y-0.5;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/50;
  }

  .btn-ghost {
    @apply bg-transparent text-slate-600 dark:text-slate-300 font-medium py-2 px-4 rounded-lg;
    @apply transition-all duration-200 hover:bg-white/10;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/50;
  }

  /* Input styles */
  .input-glass {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3;
    @apply text-slate-900 dark:text-slate-100 placeholder-slate-500 dark:placeholder-slate-400;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500/50;
    @apply focus:border-primary-500/50 focus:bg-white/20;
  }

  /* Card styles */
  .card {
    @apply glass-card;
  }

  .card-header {
    @apply border-b border-white/10 pb-4 mb-6;
  }

  .card-title {
    @apply text-xl font-semibold text-slate-900 dark:text-slate-100;
  }

  .card-description {
    @apply text-sm text-slate-600 dark:text-slate-400 mt-1;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400;
    @apply transition-colors duration-200 font-medium;
  }

  .nav-link.active {
    @apply text-primary-600 dark:text-primary-400;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.2s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-slate-300 border-t-primary-500;
  }

  /* Status indicators */
  .status-active {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .status-inactive {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
  }

  .status-expired {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  .status-suspended {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* Focus visible for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-primary-500/50;
}
