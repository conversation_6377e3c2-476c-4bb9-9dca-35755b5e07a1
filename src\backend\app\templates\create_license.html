{% extends "base.html" %}

{% block title %}Create License - <PERSON><PERSON><PERSON>th{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Create New License</h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            Generate a new license key for your software
        </p>
    </div>

    <!-- Create License Form -->
    <div class="card">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.plan_id.label(class="form-label") }}
                {{ form.plan_id(class="input-glass") }}
                {% if form.plan_id.errors %}
                    <div class="form-error">
                        {% for error in form.plan_id.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="text-sm text-slate-500 mt-1">Select the subscription plan for this license</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    {{ form.max_devices.label(class="form-label") }}
                    {{ form.max_devices(class="input-glass", placeholder="1") }}
                    {% if form.max_devices.errors %}
                        <div class="form-error">
                            {% for error in form.max_devices.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="text-sm text-slate-500 mt-1">Maximum number of devices that can use this license</p>
                </div>

                <div class="form-group">
                    {{ form.expires_at.label(class="form-label") }}
                    {{ form.expires_at(class="input-glass", placeholder="YYYY-MM-DD") }}
                    {% if form.expires_at.errors %}
                        <div class="form-error">
                            {% for error in form.expires_at.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <p class="text-sm text-slate-500 mt-1">Leave empty for no expiration</p>
                </div>
            </div>

            <div class="form-group">
                {{ form.hwid_lock.label(class="form-label") }}
                {{ form.hwid_lock(class="input-glass", placeholder="Optional hardware ID to lock this license to") }}
                {% if form.hwid_lock.errors %}
                    <div class="form-error">
                        {% for error in form.hwid_lock.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="text-sm text-slate-500 mt-1">Lock this license to a specific hardware ID (optional)</p>
            </div>

            <div class="form-group">
                {{ form.notes.label(class="form-label") }}
                {{ form.notes(class="input-glass", rows="3", placeholder="Optional notes about this license") }}
                {% if form.notes.errors %}
                    <div class="form-error">
                        {% for error in form.notes.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="flex justify-between items-center pt-6 border-t border-white/10">
                <a href="{{ url_for('licenses.list_licenses') }}" class="btn-ghost">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Licenses
                </a>
                {{ form.submit(class="btn-primary") }}
            </div>
        </form>
    </div>

    <!-- License Features Info -->
    <div class="mt-8 card">
        <div class="card-header">
            <h3 class="card-title">License Features</h3>
            <p class="card-description">What's included with your license</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="shield-check" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Secure Validation</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">HMAC-signed validation with timestamp protection</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="smartphone" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Device Management</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Track and limit device usage per license</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="activity" class="w-4 h-4 text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Real-time Analytics</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Monitor usage patterns and validation attempts</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Flexible Expiration</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Set custom expiration dates or create permanent licenses</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
