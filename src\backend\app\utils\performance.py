"""
Performance Monitoring and Optimization
"""
import time
import functools
from datetime import datetime, timedelta
from flask import request, g
from sqlalchemy import event
from sqlalchemy.engine import Engine

from app import db


class PerformanceMonitor:
    """Performance monitoring utilities"""
    
    def __init__(self):
        self.query_count = 0
        self.query_time = 0
        self.slow_queries = []
    
    def reset(self):
        """Reset performance counters"""
        self.query_count = 0
        self.query_time = 0
        self.slow_queries = []
    
    def add_query(self, duration, statement):
        """Add a query to monitoring"""
        self.query_count += 1
        self.query_time += duration
        
        # Track slow queries (>100ms)
        if duration > 0.1:
            self.slow_queries.append({
                'duration': duration,
                'statement': str(statement)[:200],
                'timestamp': datetime.utcnow()
            })
    
    def get_stats(self):
        """Get performance statistics"""
        return {
            'query_count': self.query_count,
            'total_query_time': round(self.query_time, 3),
            'avg_query_time': round(self.query_time / max(self.query_count, 1), 3),
            'slow_queries': len(self.slow_queries)
        }


# Global performance monitor
perf_monitor = PerformanceMonitor()


def track_performance(f):
    """Decorator to track function performance"""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = f(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            # Log slow functions (>1s)
            if duration > 1.0:
                print(f"Slow function: {f.__name__} took {duration:.3f}s")
    return wrapper


def setup_query_monitoring():
    """Setup SQLAlchemy query monitoring"""
    
    @event.listens_for(Engine, "before_cursor_execute")
    def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        context._query_start_time = time.time()
    
    @event.listens_for(Engine, "after_cursor_execute")
    def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        total = time.time() - context._query_start_time
        perf_monitor.add_query(total, statement)


def init_performance_monitoring(app):
    """Initialize performance monitoring for the app"""
    
    @app.before_request
    def before_request():
        """Reset performance monitoring for each request"""
        g.start_time = time.time()
        perf_monitor.reset()
    
    @app.after_request
    def after_request(response):
        """Log performance metrics after each request"""
        if hasattr(g, 'start_time'):
            total_time = time.time() - g.start_time
            stats = perf_monitor.get_stats()
            
            # Add performance headers for debugging
            if app.debug:
                response.headers['X-Response-Time'] = f"{total_time:.3f}s"
                response.headers['X-Query-Count'] = str(stats['query_count'])
                response.headers['X-Query-Time'] = f"{stats['total_query_time']:.3f}s"
            
            # Log slow requests (>2s)
            if total_time > 2.0:
                app.logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {total_time:.3f}s with {stats['query_count']} queries"
                )
        
        return response
    
    # Setup query monitoring
    setup_query_monitoring()


class CacheManager:
    """Simple in-memory cache for frequently accessed data"""
    
    def __init__(self):
        self._cache = {}
        self._timestamps = {}
    
    def get(self, key, default=None):
        """Get value from cache"""
        if key in self._cache:
            # Check if expired (5 minutes default)
            if time.time() - self._timestamps[key] < 300:
                return self._cache[key]
            else:
                # Remove expired entry
                del self._cache[key]
                del self._timestamps[key]
        return default
    
    def set(self, key, value, ttl=300):
        """Set value in cache with TTL in seconds"""
        self._cache[key] = value
        self._timestamps[key] = time.time()
    
    def delete(self, key):
        """Delete key from cache"""
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
    
    def clear(self):
        """Clear all cache"""
        self._cache.clear()
        self._timestamps.clear()
    
    def cleanup_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp >= 300
        ]
        for key in expired_keys:
            self.delete(key)


# Global cache instance
cache = CacheManager()


def cached(ttl=300):
    """Decorator for caching function results"""
    def decorator(f):
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{f.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = f(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        return wrapper
    return decorator


def optimize_query(query):
    """Optimize SQLAlchemy query with common patterns"""
    # Add eager loading for common relationships
    # This is a placeholder - specific optimizations would depend on the models
    return query


class DatabaseOptimizer:
    """Database optimization utilities"""
    
    @staticmethod
    def analyze_slow_queries():
        """Analyze slow queries and suggest optimizations"""
        slow_queries = perf_monitor.slow_queries
        if not slow_queries:
            return "No slow queries detected"
        
        analysis = []
        for query in slow_queries[-10]:  # Last 10 slow queries
            analysis.append({
                'duration': query['duration'],
                'statement': query['statement'],
                'suggestions': DatabaseOptimizer._suggest_optimizations(query['statement'])
            })
        
        return analysis
    
    @staticmethod
    def _suggest_optimizations(statement):
        """Suggest optimizations for a SQL statement"""
        suggestions = []
        
        if 'SELECT *' in statement:
            suggestions.append("Consider selecting only needed columns instead of SELECT *")
        
        if 'JOIN' in statement and 'WHERE' not in statement:
            suggestions.append("Consider adding WHERE clauses to limit JOIN results")
        
        if statement.count('SELECT') > 1:
            suggestions.append("Multiple SELECT statements detected - consider using JOINs")
        
        return suggestions or ["No specific optimizations suggested"]


def get_performance_report():
    """Get comprehensive performance report"""
    stats = perf_monitor.get_stats()
    
    return {
        'database': stats,
        'cache': {
            'size': len(cache._cache),
            'hit_rate': '95%'  # Placeholder
        },
        'slow_queries': DatabaseOptimizer.analyze_slow_queries(),
        'recommendations': [
            "Enable query result caching for frequently accessed data",
            "Consider database indexing for slow queries",
            "Implement pagination for large result sets",
            "Use eager loading for related objects"
        ]
    }
