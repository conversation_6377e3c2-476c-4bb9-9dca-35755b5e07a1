# Pepe Auth - Next-Generation Licensing & Authentication Platform

A secure, scalable, and feature-rich licensing and authentication platform built with modern technologies and a stunning glassmorphism UI. Think "KeyAuth" but more secure, more insightful, and designed for 2025.

## 🚀 Features

### Core Authentication
- **Multi-factor Authentication**: JWT + refresh tokens, TOTP 2FA, email verification
- **Advanced Security**: HMAC-SHA-256 signatures, Argon2 password hashing, 2-minute timestamp windows
- **Hardware Fingerprinting**: Optional HWID locking with device management
- **Offline Licensing**: Cryptographically signed `.lic` files for offline validation (up to 30 days)

### License Management
- **Flexible Key Formats**: 8-4-4-4-12 pattern or JSON payload, Ed25519 signed
- **Tiered Plans**: Free Trial, Hobby, Pro, Enterprise with configurable limits
- **Grace Periods**: Configurable validity windows and renewal handling
- **Geofencing**: Country-based license restrictions

### Advanced Analytics
- **Real-time Dashboards**: MAU tracking, validation error heat-maps, geo-charts
- **Fraud Detection**: Duplicate HWID detection, TOR/VPN IP flagging, anomaly detection
- **AI Insights**: LTV prediction, churn analysis, price optimization
- **Custom Queries**: ClickHouse integration for complex analytics

### Developer Experience
- **Multi-language SDKs**: Auto-generated from OpenAPI spec (Python, C#, C++, JS)
- **CLI Tools**: `licctl` for license operations and offline activation
- **Obfuscation Helpers**: PyArmor, ConfuserEx integration with CI hooks
- **Comprehensive Docs**: Interactive API documentation and tutorials

### Enterprise Features
- **Role-based Access Control**: Owner/Admin/Billing/Support/Read-only roles
- **Audit Logging**: SOC-2 compliant audit trails with export capabilities
- **White-label Mode**: Custom domains and theming
- **Compliance**: GDPR exports, SBOM generation, audit reports

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript + TailwindCSS + Framer Motion
- **Backend**: Python 3.12 + Flask 3 + SQLAlchemy + Redis
- **Database**: PostgreSQL 15 (primary) + Redis 7 (sessions/cache)
- **Security**: JWT tokens, HMAC signatures, Ed25519 cryptography
- **Deployment**: Docker + Kubernetes with GitHub Actions CI/CD

### Project Structure
```
/src
  /frontend          # React TypeScript application
    /src
      /components    # Reusable UI components
      /pages         # Page components
      /hooks         # Custom React hooks
      /utils         # Utility functions
      /types         # TypeScript type definitions
    /public          # Static assets
  /backend           # Flask API server
    /app             # Application factory and config
    /models          # SQLAlchemy models
    /routes          # API route handlers
    /services        # Business logic services
    /utils           # Backend utilities
  /shared            # Shared DTOs and constants
/config              # Environment and secrets templates
/deploy              # Docker and Kubernetes configurations
  /docker            # Dockerfiles and compose files
  /k8s               # Kubernetes manifests and Helm charts
/scripts             # CLI tools and database migrations
  /db                # Database migration scripts
  /cli               # Command-line utilities
/docs                # Documentation and API specs
/tests               # Test suites
  /unit              # Unit tests
  /integration       # Integration tests
  /e2e               # End-to-end tests
```

## 🎨 Glassmorphism UI Design

Our 2025-grade glassmorphism interface features:
- **Frosted Glass Effects**: 8-16px blur with translucent surfaces
- **High Contrast**: Strategic use of backplates for readability
- **Micro-animations**: Framer Motion with <200ms transitions
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Performance Optimized**: Backdrop-filter only on static elements

## 🚦 Getting Started

### Prerequisites
- Python 3.12+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker (optional)

### Quick Start with Docker
```bash
# Clone the repository
git clone <repository-url>
cd pepe-auth

# Start all services
docker-compose up -d

# Access the application
open http://localhost:3000
```

### Manual Setup
```bash
# Backend setup
cd src/backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
flask db upgrade
flask run

# Frontend setup (new terminal)
cd src/frontend
npm install
npm run dev
```

## 📊 API Documentation

Interactive API documentation is available at `/docs` when running the development server.

Key endpoints:
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/validate` - License validation
- `GET /api/v1/licenses` - License management
- `GET /api/v1/analytics/dashboard` - Analytics data

## 🔒 Security Features

- **Rate Limiting**: Global and per-endpoint quotas
- **Anomaly Detection**: ML-based fraud detection
- **Secure Headers**: HSTS, CSP, and security headers
- **Input Validation**: Comprehensive request validation
- **Audit Logging**: Complete action audit trail

## 🧪 Testing

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest --cov=src/backend tests/

# Run e2e tests
playwright test
```

## 📈 Deployment

### Production Deployment
```bash
# Build and deploy with Kubernetes
kubectl apply -f deploy/k8s/

# Or use Helm
helm install pepe-auth deploy/helm/
```

### Self-hosted Deployment
```bash
# Single-node Docker Compose
docker-compose -f deploy/docker/docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/pepe-auth)
- 📖 Documentation: [docs.pepe-auth.com](https://docs.pepe-auth.com)
- 🐛 Issues: [GitHub Issues](https://github.com/pepe-auth/issues)

---

Built with ❤️ by the Pepe Auth team
