{% extends "base.html" %}

{% block title %}Create API Key - P<PERSON><PERSON> Auth{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Create API Key</h1>
        <p class="text-slate-600 dark:text-slate-400 mt-2">
            Generate a new API key for programmatic access
        </p>
    </div>

    <!-- Create API Key Form -->
    <div class="card">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="input-glass", placeholder="e.g., Production Server, Mobile App") }}
                {% if form.name.errors %}
                    <div class="form-error">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="text-sm text-slate-500 mt-1">Choose a descriptive name to identify this API key</p>
            </div>

            <div class="form-group">
                {{ form.permissions.label(class="form-label") }}
                <div class="space-y-3">
                    {% for value, label in form.permissions.choices %}
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" name="permissions" value="{{ value }}" 
                                   class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <div>
                                <span class="font-medium text-slate-900 dark:text-white">{{ label }}</span>
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    {% if value == 'validate_licenses' %}
                                        Allow validation of license keys
                                    {% elif value == 'manage_licenses' %}
                                        Create, update, and revoke licenses
                                    {% elif value == 'view_analytics' %}
                                        Access analytics and usage data
                                    {% elif value == 'manage_webhooks' %}
                                        Create and manage webhook endpoints
                                    {% endif %}
                                </p>
                            </div>
                        </label>
                    {% endfor %}
                </div>
                {% if form.permissions.errors %}
                    <div class="form-error">
                        {% for error in form.permissions.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.notes.label(class="form-label") }}
                {{ form.notes(class="input-glass", rows="3", placeholder="Optional notes about this API key") }}
                {% if form.notes.errors %}
                    <div class="form-error">
                        {% for error in form.notes.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="flex justify-between items-center pt-6 border-t border-white/10">
                <a href="{{ url_for('api_keys.list_api_keys') }}" class="btn-ghost">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to API Keys
                </a>
                {{ form.submit(class="btn-primary") }}
            </div>
        </form>
    </div>

    <!-- Security Notice -->
    <div class="mt-8 card bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-lg flex items-center justify-center flex-shrink-0">
                <i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
            </div>
            <div>
                <h3 class="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Security Notice</h3>
                <div class="text-sm text-yellow-800 dark:text-yellow-200 space-y-2">
                    <p>• Your API key will be shown only once after creation. Make sure to copy and store it securely.</p>
                    <p>• Never share your API keys or commit them to version control.</p>
                    <p>• Use environment variables or secure key management systems in production.</p>
                    <p>• Regularly rotate your API keys and revoke unused ones.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key Features -->
    <div class="mt-8 card">
        <div class="card-header">
            <h3 class="card-title">API Key Features</h3>
            <p class="card-description">What you can do with API keys</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="shield-check" class="w-4 h-4 text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Secure Authentication</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">HMAC-signed requests with automatic key rotation</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="zap" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Rate Limiting</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Built-in rate limiting to prevent abuse</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="activity" class="w-4 h-4 text-purple-600 dark:text-purple-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Usage Tracking</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Monitor API usage and performance metrics</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="settings" class="w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div>
                    <h4 class="font-medium text-slate-900 dark:text-white">Granular Permissions</h4>
                    <p class="text-sm text-slate-600 dark:text-slate-400">Fine-grained access control for different operations</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
