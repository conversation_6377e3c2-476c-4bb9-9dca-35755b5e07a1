{% extends "base.html" %}

{% block title %}Webhooks - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-slate-900 dark:text-white">Webhooks</h1>
            <p class="text-slate-600 dark:text-slate-400 mt-2">
                Receive real-time notifications about events in your account
            </p>
        </div>
        <div>
            <a href="{{ url_for('webhooks.create_webhook') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create Webhook
            </a>
        </div>
    </div>

    <!-- Webhooks Table -->
    {% if webhooks %}
        <div class="card">
            <div class="overflow-x-auto">
                <table class="table-glass w-full">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>URL</th>
                            <th>Events</th>
                            <th>Last Triggered</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for webhook in webhooks %}
                            <tr>
                                <td>
                                    <div class="font-medium text-slate-900 dark:text-white">
                                        <a href="{{ url_for('webhooks.view_webhook', webhook_id=webhook.id) }}" class="text-primary-600 hover:text-primary-500">
                                            {{ webhook.name }}
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="font-mono text-sm text-slate-600 dark:text-slate-400 max-w-xs truncate">
                                        {{ webhook.url }}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex flex-wrap gap-1">
                                        {% for event in webhook.events[:3] %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                                                {{ event.split('.')[-1].title() }}
                                            </span>
                                        {% endfor %}
                                        {% if webhook.events|length > 3 %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                                                +{{ webhook.events|length - 3 }} more
                                            </span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {% if webhook.last_triggered_at %}
                                            {{ webhook.last_triggered_at.strftime('%b %d, %Y') }}
                                        {% else %}
                                            Never
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if webhook.is_active %}
                                        <span class="status-badge status-active">Active</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">Disabled</span>
                                    {% endif %}
                                    {% if webhook.failure_count > 0 %}
                                        <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                                            {{ webhook.failure_count }} failures
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="flex space-x-2">
                                        <form method="POST" action="{{ url_for('webhooks.test_webhook', webhook_id=webhook.id) }}" class="inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn-ghost text-blue-600 hover:text-blue-700" title="Test webhook">
                                                <i data-lucide="send" class="w-4 h-4"></i>
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('webhooks.toggle_webhook', webhook_id=webhook.id) }}" class="inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn-ghost text-yellow-600 hover:text-yellow-700" 
                                                    title="{% if webhook.is_active %}Disable{% else %}Enable{% endif %} webhook">
                                                {% if webhook.is_active %}
                                                    <i data-lucide="pause" class="w-4 h-4"></i>
                                                {% else %}
                                                    <i data-lucide="play" class="w-4 h-4"></i>
                                                {% endif %}
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ url_for('webhooks.delete_webhook', webhook_id=webhook.id) }}" class="inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn-ghost text-red-600 hover:text-red-700" 
                                                    onclick="return confirm('Are you sure you want to delete this webhook? This action cannot be undone.')"
                                                    title="Delete webhook">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="card text-center py-12">
            <div class="w-24 h-24 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="webhook" class="w-12 h-12 text-purple-600"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                No webhooks yet
            </h3>
            <p class="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                Create your first webhook to receive real-time notifications about events in your account.
            </p>
            <a href="{{ url_for('webhooks.create_webhook') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create Your First Webhook
            </a>
        </div>
    {% endif %}

    <!-- Webhook Documentation -->
    <div class="mt-8 card">
        <div class="card-header">
            <h3 class="card-title">Webhook Events</h3>
            <p class="card-description">Available events you can subscribe to</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-3">License Events</h4>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">license.created</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">license.validated</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">license.expired</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">license.suspended</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">license.revoked</span>
                    </div>
                </div>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-3">User & Payment Events</h4>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">user.registered</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">payment.succeeded</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span class="text-sm text-slate-600 dark:text-slate-400">payment.failed</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <h4 class="font-medium text-slate-900 dark:text-white mb-2">Webhook Security</h4>
            <p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
                All webhook payloads are signed with HMAC-SHA256. Verify the signature using the 
                <code class="bg-slate-200 dark:bg-slate-700 px-1 rounded">X-Webhook-Signature</code> header.
            </p>
            <div class="bg-slate-900 rounded p-3 mt-3">
                <pre class="text-green-400 text-xs"><code>import hmac
import hashlib

def verify_webhook(payload, signature, secret):
    expected = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(f"sha256={expected}", signature)</code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}
