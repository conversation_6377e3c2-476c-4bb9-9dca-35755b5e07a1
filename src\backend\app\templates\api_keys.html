{% extends "base.html" %}

{% block title %}API Keys - Pep<PERSON>th{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-slate-900 dark:text-white">API Keys</h1>
            <p class="text-slate-600 dark:text-slate-400 mt-2">
                Manage your API keys for programmatic access
            </p>
        </div>
        <div>
            <a href="{{ url_for('api_keys.create_api_key') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create API Key
            </a>
        </div>
    </div>

    <!-- API Keys Table -->
    {% if api_keys %}
        <div class="card">
            <div class="overflow-x-auto">
                <table class="table-glass w-full">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Key Prefix</th>
                            <th>Permissions</th>
                            <th>Last Used</th>
                            <th>Created</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for api_key in api_keys %}
                            <tr>
                                <td>
                                    <div class="font-medium text-slate-900 dark:text-white">
                                        {{ api_key.name }}
                                    </div>
                                </td>
                                <td>
                                    <span class="font-mono text-sm bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                                        {{ api_key.key_prefix }}...
                                    </span>
                                </td>
                                <td>
                                    <div class="flex flex-wrap gap-1">
                                        {% for permission, enabled in api_key.permissions.items() %}
                                            {% if enabled %}
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                                    {{ permission.replace('_', ' ').title() }}
                                                </span>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {% if api_key.last_used_at %}
                                            {{ api_key.last_used_at.strftime('%b %d, %Y') }}
                                        {% else %}
                                            Never
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-sm">
                                        {{ api_key.created_at.strftime('%b %d, %Y') }}
                                    </span>
                                </td>
                                <td>
                                    {% if api_key.is_active %}
                                        <span class="status-badge status-active">Active</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">Revoked</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if api_key.is_active %}
                                        <form method="POST" action="{{ url_for('api_keys.revoke_api_key', api_key_id=api_key.id) }}" class="inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn-ghost text-red-600 hover:text-red-700" 
                                                    onclick="return confirm('Are you sure you want to revoke this API key? This action cannot be undone.')">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </form>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="card text-center py-12">
            <div class="w-24 h-24 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="key" class="w-12 h-12 text-purple-600"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                No API keys yet
            </h3>
            <p class="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                Create your first API key to access the Pepe Auth API programmatically.
            </p>
            <a href="{{ url_for('api_keys.create_api_key') }}" class="btn-primary">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Create Your First API Key
            </a>
        </div>
    {% endif %}

    <!-- API Documentation -->
    <div class="mt-8 card">
        <div class="card-header">
            <h3 class="card-title">API Usage</h3>
            <p class="card-description">How to use your API keys</p>
        </div>
        
        <div class="space-y-6">
            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">License Validation</h4>
                <div class="bg-slate-900 rounded-lg p-4 overflow-x-auto">
                    <pre class="text-green-400 text-sm"><code>curl -X POST {{ request.url_root }}licenses/api/validate \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "license_key": "XXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
    "hwid": "unique-hardware-id",
    "timestamp": **********,
    "signature": "hmac-signature"
  }'</code></pre>
                </div>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">Authentication</h4>
                <p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
                    Include your API key in the <code class="bg-slate-100 dark:bg-slate-800 px-1 rounded">X-API-Key</code> header:
                </p>
                <div class="bg-slate-900 rounded-lg p-4 overflow-x-auto">
                    <pre class="text-green-400 text-sm"><code>X-API-Key: pk_your_api_key_here</code></pre>
                </div>
            </div>

            <div>
                <h4 class="font-medium text-slate-900 dark:text-white mb-2">Rate Limits</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <p class="font-medium text-blue-900 dark:text-blue-100">License Validation</p>
                        <p class="text-sm text-blue-700 dark:text-blue-300">1000 requests/hour</p>
                    </div>
                    <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <p class="font-medium text-green-900 dark:text-green-100">License Management</p>
                        <p class="text-sm text-green-700 dark:text-green-300">100 requests/hour</p>
                    </div>
                    <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <p class="font-medium text-purple-900 dark:text-purple-100">Analytics</p>
                        <p class="text-sm text-purple-700 dark:text-purple-300">50 requests/hour</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
